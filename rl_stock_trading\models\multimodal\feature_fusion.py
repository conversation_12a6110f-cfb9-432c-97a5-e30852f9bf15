# -*- coding: utf-8 -*-
"""
多模态特征融合模块
整合技术指标、时间序列模式和动态分块特征
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.config import *
from models.dynamic_chunking.chunking_module import DynamicChunkingModel

class CNNFeatureExtractor(nn.Module):
    """
    CNN特征提取器
    用于提取时间序列的局部模式
    """
    def __init__(self, 
                 input_channels: int = 1,
                 output_dim: int = 128,
                 channels: List[int] = CNN_CHANNELS,
                 kernel_sizes: List[int] = CNN_KERNEL_SIZES):
        super(CNNFeatureExtractor, self).__init__()
        
        self.conv_layers = nn.ModuleList()
        
        # 多尺度卷积层
        for i, (channel, kernel_size) in enumerate(zip(channels, kernel_sizes)):
            if i == 0:
                in_channels = input_channels
            else:
                in_channels = channels[i-1]
            
            conv_block = nn.Sequential(
                nn.Conv1d(in_channels, channel, kernel_size, padding=kernel_size//2),
                nn.BatchNorm1d(channel),
                nn.ReLU(),
                nn.Dropout(CNN_DROPOUT),
                nn.MaxPool1d(2)
            )
            self.conv_layers.append(conv_block)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 输出投影
        self.output_projection = nn.Linear(sum(channels), output_dim)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入时间序列 [batch_size, seq_len, n_features]
            
        Returns:
            features: 提取的特征 [batch_size, output_dim]
        """
        # 转换为CNN输入格式 [batch_size, n_features, seq_len]
        x = x.transpose(1, 2)
        
        # 多尺度特征提取
        multi_scale_features = []
        
        for conv_layer in self.conv_layers:
            x = conv_layer(x)
            # 全局池化
            pooled = self.global_pool(x).squeeze(-1)
            multi_scale_features.append(pooled)
        
        # 拼接多尺度特征
        combined_features = torch.cat(multi_scale_features, dim=1)
        
        # 输出投影
        output = self.output_projection(combined_features)
        
        return output

class LSTMFeatureExtractor(nn.Module):
    """
    LSTM特征提取器
    用于捕获长期时间依赖
    """
    def __init__(self, 
                 input_dim: int,
                 hidden_size: int = LSTM_HIDDEN_SIZE,
                 num_layers: int = LSTM_NUM_LAYERS,
                 output_dim: int = 128):
        super(LSTMFeatureExtractor, self).__init__()
        
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=LSTM_DROPOUT if num_layers > 1 else 0,
            batch_first=True,
            bidirectional=True
        )
        
        # 输出投影
        self.output_projection = nn.Linear(hidden_size * 2, output_dim)  # *2 for bidirectional
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size * 2,
            num_heads=4,
            dropout=0.1,
            batch_first=True
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入时间序列 [batch_size, seq_len, input_dim]
            
        Returns:
            features: 提取的特征 [batch_size, output_dim]
        """
        # LSTM编码
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 自注意力机制
        attended_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 取最后一个时间步的输出
        final_output = attended_out[:, -1, :]
        
        # 输出投影
        features = self.output_projection(final_output)
        
        return features

class TransformerFeatureExtractor(nn.Module):
    """
    Transformer特征提取器
    用于捕获复杂的时间序列模式
    """
    def __init__(self, 
                 input_dim: int,
                 d_model: int = TRANSFORMER_D_MODEL,
                 nhead: int = TRANSFORMER_NHEAD,
                 num_layers: int = TRANSFORMER_NUM_LAYERS,
                 output_dim: int = 128):
        super(TransformerFeatureExtractor, self).__init__()
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=TRANSFORMER_DROPOUT,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # 输出投影
        self.output_projection = nn.Linear(d_model, output_dim)
        
        # 全局池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入时间序列 [batch_size, seq_len, input_dim]
            
        Returns:
            features: 提取的特征 [batch_size, output_dim]
        """
        # 输入投影
        x = self.input_projection(x)
        
        # 位置编码
        x = self.pos_encoding(x)
        
        # Transformer编码
        encoded = self.transformer(x)
        
        # 全局池化 [batch_size, seq_len, d_model] -> [batch_size, d_model]
        pooled = encoded.mean(dim=1)
        
        # 输出投影
        features = self.output_projection(pooled)
        
        return features

class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model: int, max_len: int = 5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-torch.log(torch.tensor(10000.0)) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return x + self.pe[:, :x.size(1)]

class CrossModalAttention(nn.Module):
    """
    跨模态注意力机制
    用于融合不同模态的特征
    """
    def __init__(self, feature_dim: int = 128, num_heads: int = 8):
        super(CrossModalAttention, self).__init__()
        
        self.feature_dim = feature_dim
        self.num_heads = num_heads
        
        # 多头注意力
        self.multihead_attn = nn.MultiheadAttention(
            embed_dim=feature_dim,
            num_heads=num_heads,
            dropout=0.1,
            batch_first=True
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(feature_dim)
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim * 2, feature_dim)
        )
    
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor) -> torch.Tensor:
        """
        跨模态注意力
        
        Args:
            query: 查询特征 [batch_size, 1, feature_dim]
            key: 键特征 [batch_size, n_modalities, feature_dim]
            value: 值特征 [batch_size, n_modalities, feature_dim]
            
        Returns:
            fused_features: 融合后的特征 [batch_size, feature_dim]
        """
        # 多头注意力
        attended, _ = self.multihead_attn(query, key, value)
        
        # 残差连接和层归一化
        attended = self.layer_norm(attended + query)
        
        # 前馈网络
        output = self.ffn(attended)
        output = self.layer_norm(output + attended)
        
        return output.squeeze(1)  # [batch_size, feature_dim]

class MultiModalFeatureFusion(nn.Module):
    """
    多模态特征融合主模型
    整合CNN、LSTM、Transformer和动态分块特征
    """
    def __init__(self, 
                 input_dim: int,
                 output_dim: int = 256,
                 feature_dim: int = 128):
        super(MultiModalFeatureFusion, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.feature_dim = feature_dim
        
        # 各种特征提取器
        self.cnn_extractor = CNNFeatureExtractor(
            input_channels=input_dim, 
            output_dim=feature_dim
        )
        
        self.lstm_extractor = LSTMFeatureExtractor(
            input_dim=input_dim,
            output_dim=feature_dim
        )
        
        self.transformer_extractor = TransformerFeatureExtractor(
            input_dim=input_dim,
            output_dim=feature_dim
        )
        
        # 动态分块模型
        self.dynamic_chunking = DynamicChunkingModel(
            input_dim=input_dim,
            output_dim=feature_dim
        )
        
        # 跨模态注意力
        self.cross_modal_attention = CrossModalAttention(feature_dim)
        
        # 特征融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(feature_dim * 4, feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, output_dim)
        )
        
        # 门控机制
        self.gating_network = nn.Sequential(
            nn.Linear(feature_dim * 4, feature_dim),
            nn.ReLU(),
            nn.Linear(feature_dim, 4),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, Dict]:
        """
        多模态特征融合前向传播
        
        Args:
            x: 输入时间序列 [batch_size, seq_len, input_dim]
            
        Returns:
            fused_features: 融合后的特征 [batch_size, output_dim]
            aux_outputs: 辅助输出
        """
        batch_size = x.size(0)
        
        # 各模态特征提取
        cnn_features = self.cnn_extractor(x)
        lstm_features = self.lstm_extractor(x)
        transformer_features = self.transformer_extractor(x)
        
        # 动态分块特征
        chunking_features, chunking_aux = self.dynamic_chunking(x)
        
        # 堆叠所有特征
        all_features = torch.stack([
            cnn_features, 
            lstm_features, 
            transformer_features, 
            chunking_features
        ], dim=1)  # [batch_size, 4, feature_dim]
        
        # 跨模态注意力融合
        # 使用transformer特征作为查询
        query = transformer_features.unsqueeze(1)  # [batch_size, 1, feature_dim]
        attended_features = self.cross_modal_attention(query, all_features, all_features)
        
        # 拼接所有特征
        concatenated = torch.cat([
            cnn_features, 
            lstm_features, 
            transformer_features, 
            chunking_features
        ], dim=-1)  # [batch_size, feature_dim * 4]
        
        # 门控权重
        gate_weights = self.gating_network(concatenated)  # [batch_size, 4]
        
        # 加权融合
        weighted_features = (all_features * gate_weights.unsqueeze(-1)).sum(dim=1)
        
        # 最终融合
        final_concat = torch.cat([concatenated, weighted_features], dim=-1)
        fused_features = self.fusion_network(final_concat)
        
        # 辅助输出
        aux_outputs = {
            'cnn_features': cnn_features,
            'lstm_features': lstm_features,
            'transformer_features': transformer_features,
            'chunking_features': chunking_features,
            'gate_weights': gate_weights,
            'chunking_aux': chunking_aux
        }
        
        return fused_features, aux_outputs

if __name__ == "__main__":
    # 测试代码
    print("测试多模态特征融合...")
    
    # 创建测试数据
    batch_size = 4
    seq_len = LOOKBACK_WINDOW
    input_dim = len(TECHNICAL_FACTORS)
    
    x = torch.randn(batch_size, seq_len, input_dim)
    
    # 创建模型
    model = MultiModalFeatureFusion(input_dim, output_dim=256)
    
    # 前向传播
    fused_features, aux_outputs = model(x)
    
    print(f"输入形状: {x.shape}")
    print(f"融合特征形状: {fused_features.shape}")
    print(f"门控权重形状: {aux_outputs['gate_weights'].shape}")
    print(f"各模态特征维度: {aux_outputs['cnn_features'].shape}")
    
    print("✓ 多模态特征融合测试通过")
