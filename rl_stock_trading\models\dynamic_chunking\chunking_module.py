# -*- coding: utf-8 -*-
"""
动态分块模块实现
基于论文"Dynamic Chunking for End-to-End Hierarchical Sequence Modeling"
实现自适应的序列分块和分层建模
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Optional
import math
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from utils.config import *

class ChunkingAttention(nn.Module):
    """
    用于动态分块的注意力机制
    学习如何将序列分割成有意义的块
    """
    def __init__(self, 
                 input_dim: int,
                 hidden_dim: int = CHUNK_EMBEDDING_DIM,
                 min_chunk_size: int = MIN_CHUNK_SIZE,
                 max_chunk_size: int = MAX_CHUNK_SIZE):
        super(ChunkingAttention, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.min_chunk_size = min_chunk_size
        self.max_chunk_size = max_chunk_size
        
        # 分块决策网络
        self.chunk_scorer = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
        # 位置编码
        self.position_encoding = PositionalEncoding(input_dim)
        
        # 自注意力机制
        self.self_attention = nn.MultiheadAttention(
            embed_dim=input_dim,
            num_heads=ATTENTION_HEADS,
            dropout=ATTENTION_DROPOUT,
            batch_first=True
        )
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[List[torch.Tensor], torch.Tensor]:
        """
        动态分块前向传播
        
        Args:
            x: 输入序列 [batch_size, seq_len, input_dim]
            mask: 注意力掩码
            
        Returns:
            chunks: 分块列表
            chunk_boundaries: 分块边界 [batch_size, seq_len]
        """
        batch_size, seq_len, _ = x.shape
        
        # 添加位置编码
        x_pos = self.position_encoding(x)
        
        # 自注意力增强特征
        enhanced_x, _ = self.self_attention(x_pos, x_pos, x_pos, attn_mask=mask)
        
        # 计算分块分数
        chunk_scores = self.chunk_scorer(enhanced_x).squeeze(-1)  # [batch_size, seq_len]
        
        # 动态分块
        chunks = []
        chunk_boundaries = torch.zeros_like(chunk_scores)
        
        for b in range(batch_size):
            batch_chunks, boundaries = self._dynamic_chunk(
                enhanced_x[b], chunk_scores[b]
            )
            chunks.append(batch_chunks)
            chunk_boundaries[b] = boundaries
        
        return chunks, chunk_boundaries
    
    def _dynamic_chunk(self, sequence: torch.Tensor, scores: torch.Tensor) -> Tuple[List[torch.Tensor], torch.Tensor]:
        """
        对单个序列进行动态分块
        
        Args:
            sequence: 序列 [seq_len, input_dim]
            scores: 分块分数 [seq_len]
            
        Returns:
            chunks: 分块列表
            boundaries: 分块边界
        """
        seq_len = sequence.size(0)
        chunks = []
        boundaries = torch.zeros(seq_len)
        
        start = 0
        while start < seq_len:
            # 确定当前块的结束位置
            end = min(start + self.max_chunk_size, seq_len)
            
            # 在允许范围内寻找最佳分割点
            if end - start > self.min_chunk_size:
                # 寻找分数最高的分割点
                search_start = start + self.min_chunk_size
                search_end = min(start + self.max_chunk_size, seq_len)
                
                if search_start < search_end:
                    best_split = search_start + torch.argmax(scores[search_start:search_end]).item()
                    end = best_split + 1
            
            # 创建块
            chunk = sequence[start:end]
            chunks.append(chunk)
            
            # 标记边界
            boundaries[start:end] = len(chunks) - 1
            
            start = end
        
        return chunks, boundaries

class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model: int, max_len: int = 5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: [batch_size, seq_len, d_model]
        Returns:
            x with positional encoding added
        """
        return x + self.pe[:x.size(1), :].transpose(0, 1)

class HierarchicalEncoder(nn.Module):
    """
    分层编码器
    对不同层级的块进行编码
    """
    def __init__(self, 
                 input_dim: int,
                 hierarchy_dims: List[int] = HIERARCHY_DIMS,
                 n_levels: int = N_HIERARCHY_LEVELS):
        super(HierarchicalEncoder, self).__init__()
        
        self.input_dim = input_dim
        self.hierarchy_dims = hierarchy_dims
        self.n_levels = n_levels
        
        # 各层编码器
        self.level_encoders = nn.ModuleList()
        
        for i in range(n_levels):
            if i == 0:
                encoder_input_dim = input_dim
            else:
                encoder_input_dim = hierarchy_dims[i-1]

            # 确保注意力头数不超过维度
            n_heads = min(ATTENTION_HEADS, encoder_input_dim)
            if encoder_input_dim % n_heads != 0:
                n_heads = 1  # 如果不能整除，使用单头注意力

            encoder = nn.TransformerEncoder(
                nn.TransformerEncoderLayer(
                    d_model=encoder_input_dim,
                    nhead=n_heads,
                    dim_feedforward=hierarchy_dims[i] if i < len(hierarchy_dims) else encoder_input_dim * 2,
                    dropout=ATTENTION_DROPOUT,
                    batch_first=True
                ),
                num_layers=2
            )

            self.level_encoders.append(encoder)

            # 维度变换层 - 修复维度变换逻辑
            if i < n_levels - 1 and i < len(hierarchy_dims):
                setattr(self, f'dim_transform_{i}',
                       nn.Linear(encoder_input_dim, hierarchy_dims[i]))
    
    def forward(self, chunks: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        分层编码
        
        Args:
            chunks: 分块列表
            
        Returns:
            encoded_chunks: 编码后的分块列表
        """
        current_chunks = chunks
        encoded_levels = []
        
        for level in range(self.n_levels):
            # 对当前层级的块进行编码
            level_encoded = []
            
            for chunk in current_chunks:
                if chunk.size(0) > 0:  # 确保块不为空
                    # 添加批次维度
                    chunk_batch = chunk.unsqueeze(0)
                    
                    # 通过Transformer编码器
                    encoded_chunk = self.level_encoders[level](chunk_batch)
                    
                    # 移除批次维度
                    encoded_chunk = encoded_chunk.squeeze(0)
                    
                    level_encoded.append(encoded_chunk)
                else:
                    level_encoded.append(chunk)
            
            encoded_levels.append(level_encoded)
            
            # 为下一层准备输入（聚合当前层的块）
            if level < self.n_levels - 1:
                current_chunks = self._aggregate_chunks(level_encoded, level)
        
        return encoded_levels[-1]  # 返回最高层的编码结果
    
    def _aggregate_chunks(self, chunks: List[torch.Tensor], level: int) -> List[torch.Tensor]:
        """
        聚合块以形成下一层的输入

        Args:
            chunks: 当前层的块
            level: 当前层级

        Returns:
            aggregated_chunks: 聚合后的块
        """
        aggregated = []

        # 检查是否有维度变换层
        if not hasattr(self, f'dim_transform_{level}'):
            # 如果没有维度变换层，直接返回原始块
            return chunks

        dim_transform = getattr(self, f'dim_transform_{level}')

        # 简单策略：每两个块聚合成一个
        for i in range(0, len(chunks), 2):
            try:
                if i + 1 < len(chunks):
                    # 合并两个块
                    chunk1 = chunks[i].mean(dim=0, keepdim=True)  # 时间维度平均
                    chunk2 = chunks[i+1].mean(dim=0, keepdim=True)

                    combined = torch.cat([chunk1, chunk2], dim=0)

                    # 维度变换
                    transformed = dim_transform(combined)
                    aggregated.append(transformed)
                else:
                    # 单独处理最后一个块
                    chunk = chunks[i].mean(dim=0, keepdim=True)
                    transformed = dim_transform(chunk)
                    aggregated.append(transformed)
            except Exception as e:
                # 如果维度变换失败，使用简单的平均池化
                if i + 1 < len(chunks):
                    combined = torch.stack([chunks[i].mean(dim=0), chunks[i+1].mean(dim=0)]).mean(dim=0, keepdim=True)
                else:
                    combined = chunks[i].mean(dim=0, keepdim=True)
                aggregated.append(combined)

        return aggregated

class DynamicChunkingModel(nn.Module):
    """
    动态分块序列建模主模型
    """
    def __init__(self, 
                 input_dim: int,
                 output_dim: int,
                 hidden_dim: int = CHUNK_EMBEDDING_DIM):
        super(DynamicChunkingModel, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.hidden_dim = hidden_dim
        
        # 动态分块模块
        self.chunking_attention = ChunkingAttention(input_dim, hidden_dim)
        
        # 分层编码器
        self.hierarchical_encoder = HierarchicalEncoder(input_dim)
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(HIERARCHY_DIMS[-1], hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, output_dim)
        )
        
        # 分块损失权重
        self.chunk_loss_weight = 0.1
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, dict]:
        """
        前向传播

        Args:
            x: 输入序列 [batch_size, seq_len, input_dim]
            mask: 注意力掩码

        Returns:
            output: 模型输出 [batch_size, output_dim]
            aux_outputs: 辅助输出（用于损失计算）
        """
        batch_size = x.size(0)

        # 动态分块
        chunks_list, chunk_boundaries = self.chunking_attention(x, mask)

        # 分层编码
        encoded_outputs = []
        for batch_chunks in chunks_list:
            if batch_chunks:  # 确保有块存在
                encoded_chunks = self.hierarchical_encoder(batch_chunks)

                # 聚合所有块的表示
                if encoded_chunks:
                    chunk_representations = [chunk.mean(dim=0) for chunk in encoded_chunks]
                    batch_representation = torch.stack(chunk_representations).mean(dim=0)

                    # 确保维度正确
                    if batch_representation.dim() == 1:
                        batch_representation = batch_representation.unsqueeze(0)

                    # 动态调整维度以匹配输出投影层的期望输入
                    expected_dim = HIERARCHY_DIMS[-1]
                    actual_dim = batch_representation.size(-1)

                    if actual_dim != expected_dim:
                        # 使用线性变换调整维度
                        if not hasattr(self, 'dim_adapter'):
                            self.dim_adapter = nn.Linear(actual_dim, expected_dim).to(x.device)
                        batch_representation = self.dim_adapter(batch_representation)

                    batch_representation = batch_representation.squeeze(0)
                else:
                    batch_representation = torch.zeros(HIERARCHY_DIMS[-1]).to(x.device)
            else:
                batch_representation = torch.zeros(HIERARCHY_DIMS[-1]).to(x.device)

            encoded_outputs.append(batch_representation)

        # 堆叠批次输出
        if encoded_outputs:
            batch_representations = torch.stack(encoded_outputs)
        else:
            batch_representations = torch.zeros(batch_size, HIERARCHY_DIMS[-1]).to(x.device)

        # 输出投影
        output = self.output_projection(batch_representations)

        # 辅助输出
        aux_outputs = {
            'chunk_boundaries': chunk_boundaries,
            'n_chunks': [len(chunks) for chunks in chunks_list]
        }

        return output, aux_outputs
    
    def compute_chunking_loss(self, chunk_boundaries: torch.Tensor, target_boundaries: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算分块损失（可选）
        
        Args:
            chunk_boundaries: 预测的分块边界
            target_boundaries: 目标分块边界（如果有的话）
            
        Returns:
            chunking_loss: 分块损失
        """
        if target_boundaries is not None:
            # 如果有目标边界，计算边界预测损失
            return F.mse_loss(chunk_boundaries, target_boundaries)
        else:
            # 否则，鼓励合理的分块（避免过度分割或不足分割）
            # 计算分块数量的正则化损失
            avg_chunk_size = chunk_boundaries.size(1) / (chunk_boundaries.max(dim=1)[0] + 1)
            target_chunk_size = (MIN_CHUNK_SIZE + MAX_CHUNK_SIZE) / 2
            
            size_loss = F.mse_loss(avg_chunk_size, torch.full_like(avg_chunk_size, target_chunk_size))
            
            return size_loss

if __name__ == "__main__":
    # 测试代码
    print("测试动态分块模型...")
    
    # 创建测试数据
    batch_size = 2
    seq_len = 50
    input_dim = 64
    output_dim = 32
    
    x = torch.randn(batch_size, seq_len, input_dim)
    
    # 创建模型
    model = DynamicChunkingModel(input_dim, output_dim)
    
    # 前向传播
    output, aux_outputs = model(x)
    
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    print(f"分块边界形状: {aux_outputs['chunk_boundaries'].shape}")
    print(f"各批次分块数量: {aux_outputs['n_chunks']}")
    
    print("✓ 动态分块模型测试通过")
