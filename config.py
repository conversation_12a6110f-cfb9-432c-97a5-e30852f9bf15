import os
import torch

# 数据配置
DATA_PATH = "tushare_data_cyb/stock_factors_cyb_train.csv"  # CSV文件路径

# 创业板股票因子列表
FACTORS = [
    'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount', 
    'momentum_5d', 'ma_ratio_5d', 'volatility_5d', 'momentum_10d', 'ma_ratio_10d', 'volatility_10d', 'momentum_20d', 'ma_ratio_20d', 'volatility_20d',
    'macd', 'macd_signal', 'macd_hist', 'bb_ratio', 'atr', 'adaptive_ma', 'volume_weighted_momentum_10d', 'volume_weighted_momentum_20d', 'volatility_adjusted_momentum_10d', 'volatility_adjusted_momentum_20d',
    'price_acceleration', 'price_efficiency_ratio', 'momentum_volatility_composite_10d', 'momentum_volatility_composite_20d', 'trend_strength_10d', 'trend_strength_20d', 'overextension_factor_10d', 'overextension_factor_20d', 'pullback_opportunity', 'pe_relative',
    'pb_relative', 'mfi_14d', 'adosc', 'obv_ratio_10d', 'upper_shadow', 'lower_shadow', 'real_body_ratio', 'close_position', 'turnover_rate_anomaly_20d', 'size_factor',
    'volume_ratio', 'skew_20d', 'kurt_20d', 'downside_risk_20d', 'natr', 'kdj_k', 'kdj_d', 'kdj_j', 'volatility_regime', 'obv',
    'obv_trend', 'adx', 'plus_di', 'minus_di', 'di_diff', 'rsi', 'rsi_divergence', 'reaction_speed_factor', 'range_expansion', 'volume_price_trend',
    'normalized_composite_momentum', 'smart_money', 'stealth_accumulation', 'exhaustion_indicator', 'price_velocity_factor', 'pressure_release', 'intraday_volatility_distribution', 'price_jump_detection', 'volume_concentration', 'order_imbalance',
    'micro_momentum', 'information_ratio', 'price_discovery_efficiency', 'liquidity_consumption', 'quote_stability', 'market_depth_proxy', 'order_flow_toxicity', 'fear_index_proxy', 'greed_index_proxy', 'sentiment_transition',
    'extreme_sentiment', 'sentiment_momentum', 'dynamic_support_resistance', 'strength_persistence', 'rsi_acceleration'
]
DEFAULT_FACTORS = [
    'price_acceleration', 'rsi_acceleration', 'price_velocity_factor', 'ma_ratio_5d',
    'volatility_regime', 'close_position', 'pct_chg', 'information_ratio',
    'rsi_divergence', 'range_expansion', 'momentum_5d', 'volatility_5d',
    'size_factor', 'pb_relative', 'downside_risk_20d', 'kdj_j',
    'dynamic_support_resistance', 'skew_20d', 'volatility_adjusted_momentum_10d', 'kurt_20d',
    'amount', 'reaction_speed_factor', 'plus_di', 'stealth_accumulation',
    'natr', 'kdj_d', 'normalized_composite_momentum', 'volatility_10d',
    'liquidity_consumption', 'exhaustion_indicator', 'minus_di', 'momentum_10d',
    'sentiment_transition', 'pe_relative', 'ma_ratio_10d', 'fear_index_proxy',
    'quote_stability', 'micro_momentum', 'market_depth_proxy', 'strength_persistence',
    'intraday_volatility_distribution', 'price_discovery_efficiency', 'mfi_14d', 'smart_money',
    'sentiment_momentum', 'real_body_ratio', 'obv', 'price_jump_detection',
    'bb_ratio', 'volume_ratio', 'lower_shadow', 'trend_strength_20d',
    'volume_weighted_momentum_10d', 'volatility_20d', 'adx', 'extreme_sentiment',
    'kdj_k', 'volume_price_trend', 'volatility_adjusted_momentum_20d', 'trend_strength_10d',
    'rsi', 'macd_hist', 'upper_shadow', 'greed_index_proxy',
]
# 目标变量

TARGET = "target_return_high"

# 模型配置 - 无平均操作版本，最大化敏感性
LOOKBACK_WINDOW = 15  # 历史数据窗口大小 - 进一步减少滞后
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 64          # 模型维度
N_LAYER = 3           # Transformer层数 - 进一步减少过度平滑
NUM_HEADS = 4         # 多头注意力头数 - 减少计算复杂度
GNN_K = 2             # 图神经网络的多项式阶数 - 最小化平滑
NODE_EMBEDDING_DIM = 16  # 节点嵌入维度 - 减少参数
CNN_BLOCKS = 2               # Inception块的数量 - 减少平滑
CNN_KERNEL_SIZES = [3, 5] # 单一卷积核 - 避免多尺度平均
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.1        # 极低dropout，最大化信息保留

'''
LOOKBACK_WINDOW = 10  # 历史数据窗口大小
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 64          # 模型维度
N_LAYER = 6           # Transformer层数
NUM_HEADS = 8         # 多头注意力头数
GNN_K = 4             # 图神经网络的多项式阶数
NODE_EMBEDDING_DIM = 32  # 节点嵌入维度
CNN_BLOCKS = 2               # Inception块的数量
CNN_KERNEL_SIZES = [3, 5, 7] # 多尺度卷积核大小列表
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.1
'''


# 训练配置
EPOCHS = 300         # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-4   # 权重衰减
SAVE_INTERVAL = 10    # 权重保存间隔
EARLY_STOP_PATIENCE = 30  # 早停耐心

# 路径配置
OUTPUT_DIR = "output"  # 输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
