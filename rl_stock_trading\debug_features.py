#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试特征维度问题
"""

import torch
import numpy as np
import sys
import os
sys.path.append('.')

from utils.config import *
from utils.feature_utils import FeatureProcessor
from models.multimodal.feature_fusion import MultiModalFeatureFusion

def debug_feature_dimensions():
    """调试特征维度"""
    print("调试特征维度问题...")
    
    # 模拟环境状态
    n_features = len(TECHNICAL_FACTORS)
    seq_len = LOOKBACK_WINDOW
    n_stocks = 2  # 假设2只股票
    portfolio_features = n_stocks + 3  # 各股票仓位 + 现金比例 + 总价值 + 日收益率
    
    print(f"技术因子数量: {n_features}")
    print(f"回看窗口: {seq_len}")
    print(f"股票数量: {n_stocks}")
    print(f"投资组合特征数: {portfolio_features}")
    
    # 创建原始状态
    tech_state_size = n_features * seq_len
    total_state_size = tech_state_size + portfolio_features
    
    print(f"技术状态大小: {tech_state_size}")
    print(f"总状态大小: {total_state_size}")
    
    state = np.random.randn(total_state_size)
    print(f"原始状态形状: {state.shape}")
    
    # 创建特征融合模型
    feature_fusion = MultiModalFeatureFusion(n_features, output_dim=256)
    
    # 创建特征处理器
    processor = FeatureProcessor(feature_fusion)
    
    # 提取技术特征
    tech_features = processor.extract_technical_features(state)
    print(f"技术特征形状: {tech_features.shape}")
    
    # 提取投资组合特征
    portfolio_feats = processor.extract_portfolio_features(state)
    print(f"投资组合特征形状: {portfolio_feats.shape}")
    
    # 测试特征融合
    tech_tensor = torch.FloatTensor(tech_features).unsqueeze(0)
    print(f"技术特征张量形状: {tech_tensor.shape}")
    
    try:
        with torch.no_grad():
            fused_features, _ = feature_fusion(tech_tensor)
            print(f"融合特征形状: {fused_features.shape}")
            fused_features_np = fused_features.cpu().numpy().flatten()
            print(f"融合特征numpy形状: {fused_features_np.shape}")
            
            # 拼接最终特征
            enhanced_state = np.concatenate([fused_features_np, portfolio_feats])
            print(f"增强状态形状: {enhanced_state.shape}")
            
            return enhanced_state.shape[0]
            
    except Exception as e:
        print(f"特征融合失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    expected_dim = debug_feature_dimensions()
    print(f"\n期望的增强状态维度: {expected_dim}")
