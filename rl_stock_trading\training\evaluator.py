# -*- coding: utf-8 -*-
"""
性能评估器
用于评估强化学习股票交易模型的性能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *

class PerformanceEvaluator:
    """
    性能评估器
    计算各种金融指标和风险指标
    """
    
    def __init__(self, risk_free_rate: float = RISK_FREE_RATE):
        """
        初始化评估器
        
        Args:
            risk_free_rate: 无风险利率
        """
        self.risk_free_rate = risk_free_rate
    
    def calculate_score(self, portfolio_stats: Dict) -> float:
        """
        计算综合评分
        
        Args:
            portfolio_stats: 投资组合统计信息
            
        Returns:
            综合评分
        """
        # 权重配置
        weights = {
            'return': 0.4,
            'sharpe': 0.3,
            'drawdown': -0.2,  # 负权重，回撤越小越好
            'calmar': 0.1
        }
        
        # 获取各项指标
        total_return = portfolio_stats.get('total_return', 0)
        sharpe_ratio = portfolio_stats.get('sharpe_ratio', 0)
        max_drawdown = portfolio_stats.get('max_drawdown', 0)
        calmar_ratio = portfolio_stats.get('calmar_ratio', 0)
        
        # 标准化指标
        normalized_return = self._normalize_return(total_return)
        normalized_sharpe = self._normalize_sharpe(sharpe_ratio)
        normalized_drawdown = self._normalize_drawdown(max_drawdown)
        normalized_calmar = self._normalize_calmar(calmar_ratio)
        
        # 计算加权评分
        score = (weights['return'] * normalized_return +
                weights['sharpe'] * normalized_sharpe +
                weights['drawdown'] * normalized_drawdown +
                weights['calmar'] * normalized_calmar)
        
        return score
    
    def _normalize_return(self, total_return: float) -> float:
        """标准化收益率"""
        # 使用tanh函数将收益率映射到[-1, 1]
        return np.tanh(total_return * 2)  # 乘以2增加敏感度
    
    def _normalize_sharpe(self, sharpe_ratio: float) -> float:
        """标准化夏普比率"""
        # 夏普比率大于2认为是优秀的
        return np.tanh(sharpe_ratio / 2)
    
    def _normalize_drawdown(self, max_drawdown: float) -> float:
        """标准化最大回撤"""
        # 回撤越小越好，使用负值
        return -np.tanh(max_drawdown * 5)  # 乘以5增加惩罚
    
    def _normalize_calmar(self, calmar_ratio: float) -> float:
        """标准化卡尔玛比率"""
        return np.tanh(calmar_ratio / 2)
    
    def evaluate_portfolio(self, 
                          portfolio_values: List[float],
                          benchmark_values: Optional[List[float]] = None) -> Dict:
        """
        评估投资组合性能
        
        Args:
            portfolio_values: 投资组合价值序列
            benchmark_values: 基准价值序列
            
        Returns:
            评估结果字典
        """
        if len(portfolio_values) < 2:
            return {}
        
        # 计算收益率
        returns = self._calculate_returns(portfolio_values)
        
        # 基础指标
        metrics = {
            'total_return': self._calculate_total_return(portfolio_values),
            'annualized_return': self._calculate_annualized_return(returns),
            'volatility': self._calculate_volatility(returns),
            'sharpe_ratio': self._calculate_sharpe_ratio(returns),
            'max_drawdown': self._calculate_max_drawdown(portfolio_values),
            'calmar_ratio': self._calculate_calmar_ratio(returns, portfolio_values),
            'sortino_ratio': self._calculate_sortino_ratio(returns),
            'win_rate': self._calculate_win_rate(returns),
            'profit_loss_ratio': self._calculate_profit_loss_ratio(returns),
            'var_95': self._calculate_var(returns, 0.05),
            'cvar_95': self._calculate_cvar(returns, 0.05)
        }
        
        # 如果有基准，计算相对指标
        if benchmark_values is not None and len(benchmark_values) == len(portfolio_values):
            benchmark_metrics = self._calculate_benchmark_metrics(
                portfolio_values, benchmark_values
            )
            metrics.update(benchmark_metrics)
        
        return metrics
    
    def _calculate_returns(self, values: List[float]) -> np.ndarray:
        """计算收益率序列"""
        values = np.array(values)
        returns = np.diff(values) / values[:-1]
        return returns
    
    def _calculate_total_return(self, values: List[float]) -> float:
        """计算总收益率"""
        if len(values) < 2:
            return 0.0
        return (values[-1] - values[0]) / values[0]
    
    def _calculate_annualized_return(self, returns: np.ndarray) -> float:
        """计算年化收益率"""
        if len(returns) == 0:
            return 0.0
        
        # 假设252个交易日
        mean_return = np.mean(returns)
        annualized = (1 + mean_return) ** 252 - 1
        return annualized
    
    def _calculate_volatility(self, returns: np.ndarray) -> float:
        """计算波动率"""
        if len(returns) == 0:
            return 0.0
        return np.std(returns) * np.sqrt(252)
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray) -> float:
        """计算夏普比率"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - self.risk_free_rate / 252
        if np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    
    def _calculate_max_drawdown(self, values: List[float]) -> float:
        """计算最大回撤"""
        if len(values) < 2:
            return 0.0
        
        values = np.array(values)
        peak = np.maximum.accumulate(values)
        drawdown = (peak - values) / peak
        return np.max(drawdown)
    
    def _calculate_calmar_ratio(self, returns: np.ndarray, values: List[float]) -> float:
        """计算卡尔玛比率"""
        annualized_return = self._calculate_annualized_return(returns)
        max_drawdown = self._calculate_max_drawdown(values)
        
        if max_drawdown == 0:
            return 0.0
        
        return annualized_return / max_drawdown
    
    def _calculate_sortino_ratio(self, returns: np.ndarray) -> float:
        """计算索提诺比率"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - self.risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            return 0.0
        
        downside_deviation = np.std(downside_returns) * np.sqrt(252)
        
        if downside_deviation == 0:
            return 0.0
        
        return np.mean(excess_returns) * np.sqrt(252) / downside_deviation
    
    def _calculate_win_rate(self, returns: np.ndarray) -> float:
        """计算胜率"""
        if len(returns) == 0:
            return 0.0
        
        winning_days = np.sum(returns > 0)
        return winning_days / len(returns)
    
    def _calculate_profit_loss_ratio(self, returns: np.ndarray) -> float:
        """计算盈亏比"""
        if len(returns) == 0:
            return 0.0
        
        profits = returns[returns > 0]
        losses = returns[returns < 0]
        
        if len(profits) == 0 or len(losses) == 0:
            return 0.0
        
        avg_profit = np.mean(profits)
        avg_loss = np.mean(np.abs(losses))
        
        return avg_profit / avg_loss
    
    def _calculate_var(self, returns: np.ndarray, alpha: float) -> float:
        """计算VaR"""
        if len(returns) == 0:
            return 0.0
        
        return np.percentile(returns, alpha * 100)
    
    def _calculate_cvar(self, returns: np.ndarray, alpha: float) -> float:
        """计算CVaR"""
        if len(returns) == 0:
            return 0.0
        
        var = self._calculate_var(returns, alpha)
        tail_losses = returns[returns <= var]
        
        if len(tail_losses) == 0:
            return var
        
        return np.mean(tail_losses)
    
    def _calculate_benchmark_metrics(self, 
                                   portfolio_values: List[float],
                                   benchmark_values: List[float]) -> Dict:
        """计算相对基准的指标"""
        portfolio_returns = self._calculate_returns(portfolio_values)
        benchmark_returns = self._calculate_returns(benchmark_values)
        
        # 超额收益
        excess_returns = portfolio_returns - benchmark_returns
        
        metrics = {
            'alpha': np.mean(excess_returns) * 252,
            'beta': self._calculate_beta(portfolio_returns, benchmark_returns),
            'information_ratio': self._calculate_information_ratio(excess_returns),
            'tracking_error': np.std(excess_returns) * np.sqrt(252)
        }
        
        return metrics
    
    def _calculate_beta(self, portfolio_returns: np.ndarray, benchmark_returns: np.ndarray) -> float:
        """计算贝塔系数"""
        if len(portfolio_returns) == 0 or len(benchmark_returns) == 0:
            return 0.0
        
        covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        
        if benchmark_variance == 0:
            return 0.0
        
        return covariance / benchmark_variance
    
    def _calculate_information_ratio(self, excess_returns: np.ndarray) -> float:
        """计算信息比率"""
        if len(excess_returns) == 0:
            return 0.0
        
        tracking_error = np.std(excess_returns)
        
        if tracking_error == 0:
            return 0.0
        
        return np.mean(excess_returns) / tracking_error * np.sqrt(252)
    
    def generate_performance_report(self, 
                                  metrics: Dict,
                                  save_path: Optional[str] = None) -> str:
        """
        生成性能报告
        
        Args:
            metrics: 性能指标字典
            save_path: 保存路径
            
        Returns:
            报告文本
        """
        report_lines = [
            "=" * 60,
            "投资组合性能报告",
            "=" * 60,
            "",
            "收益指标:",
            f"  总收益率: {metrics.get('total_return', 0):.4f} ({metrics.get('total_return', 0)*100:.2f}%)",
            f"  年化收益率: {metrics.get('annualized_return', 0):.4f} ({metrics.get('annualized_return', 0)*100:.2f}%)",
            "",
            "风险指标:",
            f"  波动率: {metrics.get('volatility', 0):.4f} ({metrics.get('volatility', 0)*100:.2f}%)",
            f"  最大回撤: {metrics.get('max_drawdown', 0):.4f} ({metrics.get('max_drawdown', 0)*100:.2f}%)",
            f"  VaR (95%): {metrics.get('var_95', 0):.4f}",
            f"  CVaR (95%): {metrics.get('cvar_95', 0):.4f}",
            "",
            "风险调整收益:",
            f"  夏普比率: {metrics.get('sharpe_ratio', 0):.4f}",
            f"  索提诺比率: {metrics.get('sortino_ratio', 0):.4f}",
            f"  卡尔玛比率: {metrics.get('calmar_ratio', 0):.4f}",
            "",
            "交易指标:",
            f"  胜率: {metrics.get('win_rate', 0):.4f} ({metrics.get('win_rate', 0)*100:.2f}%)",
            f"  盈亏比: {metrics.get('profit_loss_ratio', 0):.4f}",
            ""
        ]
        
        # 如果有基准指标
        if 'alpha' in metrics:
            report_lines.extend([
                "相对基准指标:",
                f"  Alpha: {metrics.get('alpha', 0):.4f}",
                f"  Beta: {metrics.get('beta', 0):.4f}",
                f"  信息比率: {metrics.get('information_ratio', 0):.4f}",
                f"  跟踪误差: {metrics.get('tracking_error', 0):.4f}",
                ""
            ])
        
        report_lines.append("=" * 60)
        
        report_text = "\n".join(report_lines)
        
        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(report_text)
        
        return report_text
    
    def plot_performance_charts(self, 
                              portfolio_values: List[float],
                              benchmark_values: Optional[List[float]] = None,
                              save_path: Optional[str] = None):
        """
        绘制性能图表
        
        Args:
            portfolio_values: 投资组合价值
            benchmark_values: 基准价值
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 净值曲线
        axes[0, 0].plot(portfolio_values, label='Portfolio', color=COLORS['profit'])
        if benchmark_values:
            axes[0, 0].plot(benchmark_values, label='Benchmark', color=COLORS['benchmark'])
        axes[0, 0].set_title('Portfolio Value')
        axes[0, 0].set_xlabel('Time')
        axes[0, 0].set_ylabel('Value')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 收益率分布
        returns = self._calculate_returns(portfolio_values)
        axes[0, 1].hist(returns, bins=50, alpha=0.7, color=COLORS['neutral'])
        axes[0, 1].set_title('Returns Distribution')
        axes[0, 1].set_xlabel('Returns')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].grid(True)
        
        # 回撤曲线
        values = np.array(portfolio_values)
        peak = np.maximum.accumulate(values)
        drawdown = (peak - values) / peak
        axes[1, 0].fill_between(range(len(drawdown)), drawdown, 0, 
                               color=COLORS['loss'], alpha=0.7)
        axes[1, 0].set_title('Drawdown')
        axes[1, 0].set_xlabel('Time')
        axes[1, 0].set_ylabel('Drawdown')
        axes[1, 0].grid(True)
        
        # 滚动夏普比率
        if len(returns) > 20:
            rolling_sharpe = []
            window = 20
            for i in range(window, len(returns)):
                window_returns = returns[i-window:i]
                sharpe = self._calculate_sharpe_ratio(window_returns)
                rolling_sharpe.append(sharpe)
            
            axes[1, 1].plot(range(window, len(returns)), rolling_sharpe, 
                           color=COLORS['profit'])
            axes[1, 1].set_title('Rolling Sharpe Ratio (20-day)')
            axes[1, 1].set_xlabel('Time')
            axes[1, 1].set_ylabel('Sharpe Ratio')
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=DPI)
            plt.close()
        else:
            plt.show()

if __name__ == "__main__":
    # 测试代码
    print("测试性能评估器...")
    
    evaluator = PerformanceEvaluator()
    
    # 创建模拟投资组合数据
    np.random.seed(42)
    n_days = 252
    initial_value = 100000
    
    # 模拟投资组合价值
    returns = np.random.normal(0.001, 0.02, n_days)  # 日收益率
    portfolio_values = [initial_value]
    
    for ret in returns:
        new_value = portfolio_values[-1] * (1 + ret)
        portfolio_values.append(new_value)
    
    # 评估性能
    metrics = evaluator.evaluate_portfolio(portfolio_values)
    
    print("性能指标:")
    for key, value in metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # 计算综合评分
    score = evaluator.calculate_score(metrics)
    print(f"\n综合评分: {score:.4f}")
    
    # 生成报告
    report = evaluator.generate_performance_report(metrics)
    print("\n" + report)
    
    print("✓ 性能评估器测试通过")
