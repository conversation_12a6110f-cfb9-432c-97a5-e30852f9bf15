# -*- coding: utf-8 -*-
"""
股票交易强化学习环境
模拟真实的股票交易环境，支持多股票组合交易
"""

import numpy as np
import pandas as pd
import torch
from typing import List, Tuple, Optional, Any
import gym
from gym import spaces
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *
from environment.reward_functions import *

class StockTradingEnv(gym.Env):
    """
    股票交易环境
    支持多股票组合交易和风险管理
    """
    
    def __init__(self, 
                 data: pd.DataFrame,
                 initial_capital: float = INITIAL_CAPITAL,
                 transaction_cost: float = TRANSACTION_COST,
                 max_position_size: float = MAX_POSITION_SIZE,
                 lookback_window: int = LOOKBACK_WINDOW,
                 features: List[str] = TECHNICAL_FACTORS):
        """
        初始化交易环境
        
        Args:
            data: 股票数据DataFrame，包含所有技术指标
            initial_capital: 初始资金
            transaction_cost: 交易成本
            max_position_size: 最大单只股票仓位
            lookback_window: 历史数据窗口
            features: 特征列表
        """
        super(StockTradingEnv, self).__init__()
        
        self.data = data.copy()
        self.initial_capital = initial_capital
        self.transaction_cost = transaction_cost
        self.max_position_size = max_position_size
        self.lookback_window = lookback_window
        self.features = features
        
        # 数据预处理
        self._preprocess_data()
        
        # 环境状态
        self.current_step = 0
        self.max_steps = len(self.data) - lookback_window - PREDICTION_HORIZON
        
        # 投资组合状态
        self.cash = initial_capital
        self.positions = {}  # {stock_code: shares}
        self.portfolio_value = initial_capital
        self.portfolio_history = []
        
        # 交易记录
        self.trade_history = []
        self.reward_history = []
        
        # 动作和观察空间
        self.n_stocks = len(self.stock_codes)
        
        # 动作空间：每只股票的动作 (卖出=-1, 持有=0, 买入=1)
        self.action_space = spaces.Box(
            low=-1, high=1, 
            shape=(self.n_stocks,), 
            dtype=np.float32
        )
        
        # 观察空间：技术指标 + 投资组合状态
        obs_dim = len(features) * lookback_window + self.n_stocks + 3  # +3 for cash, total_value, day_return
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(obs_dim,),
            dtype=np.float32
        )
        
        # 奖励函数
        self.reward_calculator = PortfolioRewardCalculator()
        
        # 风险管理
        self.max_drawdown = 0.0
        self.peak_value = initial_capital
    
    def _preprocess_data(self):
        """预处理数据"""
        # 确保数据按日期排序
        if 'trade_date' in self.data.columns:
            self.data = self.data.sort_values('trade_date')
        
        # 获取股票代码列表
        if 'ts_code' in self.data.columns:
            self.stock_codes = self.data['ts_code'].unique().tolist()
        else:
            self.stock_codes = ['default_stock']
        
        # 数据标准化
        for feature in self.features:
            if feature in self.data.columns:
                self.data[feature] = (self.data[feature] - self.data[feature].mean()) / (self.data[feature].std() + 1e-8)
        
        # 创建股票数据字典
        self.stock_data = {}
        for stock_code in self.stock_codes:
            if 'ts_code' in self.data.columns:
                stock_df = self.data[self.data['ts_code'] == stock_code].copy()
            else:
                stock_df = self.data.copy()
            
            stock_df = stock_df.reset_index(drop=True)
            self.stock_data[stock_code] = stock_df
    
    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_step = 0
        self.cash = self.initial_capital
        self.positions = {stock: 0 for stock in self.stock_codes}
        self.portfolio_value = self.initial_capital
        self.portfolio_history = [self.initial_capital]
        self.trade_history = []
        self.reward_history = []
        self.max_drawdown = 0.0
        self.peak_value = self.initial_capital
        
        return self._get_observation()
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, dict]:
        """
        执行一步交易
        
        Args:
            action: 动作数组，每个元素对应一只股票的动作
            
        Returns:
            observation: 新的观察
            reward: 奖励
            done: 是否结束
            info: 额外信息
        """
        # 执行交易
        trade_info = self._execute_trades(action)
        
        # 更新时间步
        self.current_step += 1
        
        # 计算投资组合价值
        new_portfolio_value = self._calculate_portfolio_value()
        
        # 计算奖励
        reward = self.reward_calculator.calculate_reward(
            old_value=self.portfolio_value,
            new_value=new_portfolio_value,
            trades=trade_info['trades'],
            positions=self.positions,
            cash=self.cash
        )
        
        # 更新状态
        self.portfolio_value = new_portfolio_value
        self.portfolio_history.append(new_portfolio_value)
        self.reward_history.append(reward)
        
        # 更新风险指标
        self._update_risk_metrics()
        
        # 检查是否结束
        done = self.current_step >= self.max_steps or self.portfolio_value <= 0
        
        # 获取新观察
        observation = self._get_observation()
        
        # 信息字典
        info = {
            'portfolio_value': self.portfolio_value,
            'cash': self.cash,
            'positions': self.positions.copy(),
            'trades': trade_info['trades'],
            'transaction_costs': trade_info['total_cost'],
            'max_drawdown': self.max_drawdown,
            'step': self.current_step
        }
        
        return observation, reward, done, info
    
    def _execute_trades(self, action: np.ndarray) -> dict:
        """
        执行交易动作
        
        Args:
            action: 动作数组
            
        Returns:
            交易信息字典
        """
        trades = []
        total_cost = 0.0
        
        for i, stock_code in enumerate(self.stock_codes):
            if i >= len(action):
                continue
                
            action_value = action[i]
            current_price = self._get_current_price(stock_code)
            
            if current_price is None or current_price <= 0:
                continue
            
            # 将连续动作转换为离散动作
            if action_value > 0.33:  # 买入
                trade_action = 'buy'
                trade_amount = abs(action_value)
            elif action_value < -0.33:  # 卖出
                trade_action = 'sell'
                trade_amount = abs(action_value)
            else:  # 持有
                continue
            
            # 执行交易
            if trade_action == 'buy':
                # 计算可买入的股数
                max_investment = min(
                    self.cash * self.max_position_size,  # 最大仓位限制
                    self.cash * trade_amount  # 动作强度
                )
                
                if max_investment > current_price:
                    shares_to_buy = int(max_investment / current_price)
                    cost = shares_to_buy * current_price
                    transaction_fee = cost * self.transaction_cost
                    total_transaction_cost = cost + transaction_fee
                    
                    if self.cash >= total_transaction_cost:
                        self.cash -= total_transaction_cost
                        self.positions[stock_code] += shares_to_buy
                        total_cost += transaction_fee
                        
                        trades.append({
                            'stock': stock_code,
                            'action': 'buy',
                            'shares': shares_to_buy,
                            'price': current_price,
                            'cost': cost,
                            'fee': transaction_fee
                        })
            
            elif trade_action == 'sell':
                # 计算可卖出的股数
                current_shares = self.positions.get(stock_code, 0)
                if current_shares > 0:
                    shares_to_sell = int(current_shares * trade_amount)
                    shares_to_sell = min(shares_to_sell, current_shares)
                    
                    if shares_to_sell > 0:
                        revenue = shares_to_sell * current_price
                        transaction_fee = revenue * self.transaction_cost
                        net_revenue = revenue - transaction_fee
                        
                        self.cash += net_revenue
                        self.positions[stock_code] -= shares_to_sell
                        total_cost += transaction_fee
                        
                        trades.append({
                            'stock': stock_code,
                            'action': 'sell',
                            'shares': shares_to_sell,
                            'price': current_price,
                            'revenue': revenue,
                            'fee': transaction_fee
                        })
        
        self.trade_history.extend(trades)
        
        return {
            'trades': trades,
            'total_cost': total_cost
        }
    
    def _get_current_price(self, stock_code: str) -> Optional[float]:
        """获取当前股票价格"""
        if stock_code not in self.stock_data:
            return None
        
        stock_df = self.stock_data[stock_code]
        if self.current_step >= len(stock_df):
            return None
        
        # 使用收盘价作为交易价格
        if 'close' in stock_df.columns:
            return stock_df.iloc[self.current_step]['close']
        else:
            return 1.0  # 默认价格
    
    def _calculate_portfolio_value(self) -> float:
        """计算投资组合总价值"""
        total_value = self.cash
        
        for stock_code, shares in self.positions.items():
            if shares > 0:
                current_price = self._get_current_price(stock_code)
                if current_price is not None:
                    total_value += shares * current_price
        
        return total_value
    
    def _get_observation(self) -> np.ndarray:
        """获取当前观察"""
        # 使用市场平均技术指标特征，而不是为每只股票单独创建特征
        # 这样可以避免维度爆炸，同时保持合理的特征表示

        all_stock_features = []

        for stock_code in self.stock_codes:
            stock_df = self.stock_data[stock_code]

            # 获取历史窗口数据
            start_idx = max(0, self.current_step - self.lookback_window + 1)
            end_idx = self.current_step + 1

            window_data = stock_df.iloc[start_idx:end_idx]

            # 如果数据不足，用零填充
            if len(window_data) < self.lookback_window:
                padding = np.zeros((self.lookback_window - len(window_data), len(self.features)))
                features_array = np.vstack([padding, window_data[self.features].values])
            else:
                features_array = window_data[self.features].values

            all_stock_features.append(features_array)

        # 计算所有股票的平均技术特征
        if all_stock_features:
            # 堆叠所有股票特征并计算平均值
            stacked_features = np.stack(all_stock_features, axis=0)  # [n_stocks, seq_len, n_features]
            tech_features = np.mean(stacked_features, axis=0)  # [seq_len, n_features]
            tech_features = tech_features.flatten()  # [seq_len * n_features]
        else:
            tech_features = np.zeros(len(self.features) * self.lookback_window)
        
        # 投资组合状态
        portfolio_features = []
        
        # 现金比例
        cash_ratio = self.cash / self.portfolio_value if self.portfolio_value > 0 else 0
        portfolio_features.append(cash_ratio)
        
        # 总价值（标准化）
        normalized_value = self.portfolio_value / self.initial_capital
        portfolio_features.append(normalized_value)
        
        # 日收益率
        if len(self.portfolio_history) > 1:
            daily_return = (self.portfolio_value - self.portfolio_history[-2]) / self.portfolio_history[-2]
        else:
            daily_return = 0.0
        portfolio_features.append(daily_return)
        
        # 各股票仓位比例
        for stock_code in self.stock_codes:
            shares = self.positions.get(stock_code, 0)
            current_price = self._get_current_price(stock_code)
            
            if current_price is not None and self.portfolio_value > 0:
                position_value = shares * current_price
                position_ratio = position_value / self.portfolio_value
            else:
                position_ratio = 0.0
            
            portfolio_features.append(position_ratio)
        
        # 拼接所有特征
        observation = np.concatenate([tech_features, portfolio_features])
        
        return observation.astype(np.float32)
    
    def _update_risk_metrics(self):
        """更新风险指标"""
        # 更新最大回撤
        if self.portfolio_value > self.peak_value:
            self.peak_value = self.portfolio_value
        
        current_drawdown = (self.peak_value - self.portfolio_value) / self.peak_value
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
    
    def get_portfolio_stats(self) -> dict:
        """获取投资组合统计信息"""
        if len(self.portfolio_history) < 2:
            return {}
        
        returns = np.diff(self.portfolio_history) / self.portfolio_history[:-1]
        
        stats = {
            'total_return': (self.portfolio_value - self.initial_capital) / self.initial_capital,
            'annualized_return': np.mean(returns) * 252,  # 假设252个交易日
            'volatility': np.std(returns) * np.sqrt(252),
            'sharpe_ratio': np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252),
            'max_drawdown': self.max_drawdown,
            'total_trades': len(self.trade_history),
            'final_cash': self.cash,
            'final_positions': self.positions.copy()
        }
        
        return stats

if __name__ == "__main__":
    # 测试代码
    print("测试股票交易环境...")
    
    # 创建模拟数据
    np.random.seed(42)
    n_days = 100
    n_stocks = 2
    
    data = []
    for stock_id in range(n_stocks):
        for day in range(n_days):
            row = {
                'ts_code': f'stock_{stock_id}',
                'trade_date': f'2024-01-{day+1:02d}',
                'close': 10 + np.random.randn() * 0.5,
                'open': 10 + np.random.randn() * 0.5,
                'high': 10.5 + np.random.randn() * 0.3,
                'low': 9.5 + np.random.randn() * 0.3,
                'vol': 1000 + np.random.randn() * 100
            }
            
            # 添加技术指标
            for factor in TECHNICAL_FACTORS:
                if factor not in row:
                    row[factor] = np.random.randn()
            
            data.append(row)
    
    df = pd.DataFrame(data)
    
    # 创建环境
    env = StockTradingEnv(df)
    
    # 测试环境
    obs = env.reset()
    print(f"观察空间维度: {obs.shape}")
    print(f"动作空间维度: {env.action_space.shape}")
    
    # 执行几步
    for i in range(5):
        action = env.action_space.sample()
        obs, reward, done, info = env.step(action)
        print(f"步骤 {i+1}: 奖励={reward:.4f}, 投资组合价值={info['portfolio_value']:.2f}")
        
        if done:
            break
    
    # 获取统计信息
    stats = env.get_portfolio_stats()
    print(f"总收益率: {stats.get('total_return', 0):.4f}")
    
    print("✓ 股票交易环境测试通过")
