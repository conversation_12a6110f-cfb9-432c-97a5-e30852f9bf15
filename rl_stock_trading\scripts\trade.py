#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
深度强化学习股票交易脚本
使用训练好的模型进行实际交易决策
"""

import argparse
import pandas as pd
import numpy as np
import torch
import logging
import sys
import os
from datetime import datetime, timedelta
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import *
from agents.multi_agent import MultiAgentDQN
from models.multimodal.feature_fusion import MultiModalFeatureFusion
from environment.trading_env import StockTradingEnv
from data.data_loader import StockDataLoader
from training.evaluator import PerformanceEvaluator
from utils.metrics import MetricsCalculator

class RLStockTrader:
    """
    强化学习股票交易器
    """
    
    def __init__(self, 
                 model_path: str,
                 data_path: str,
                 initial_capital: float = INITIAL_CAPITAL):
        """
        初始化交易器
        
        Args:
            model_path: 模型路径前缀
            data_path: 数据路径
            initial_capital: 初始资金
        """
        self.model_path = model_path
        self.data_path = data_path
        self.initial_capital = initial_capital
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 加载数据
        self.data_loader = StockDataLoader()
        self.data = self._load_trading_data()
        
        # 创建环境
        self.env = StockTradingEnv(
            self.data,
            initial_capital=initial_capital
        )
        
        # 加载模型
        self.multi_agent = self._load_model()
        self.feature_fusion = self._load_feature_fusion_model()
        
        # 评估器
        self.evaluator = PerformanceEvaluator()
        self.metrics_calculator = MetricsCalculator()
        
        # 交易记录
        self.trading_log = []
        self.portfolio_history = []
        
        self.logger.info("交易器初始化完成")
    
    def _load_trading_data(self) -> pd.DataFrame:
        """加载交易数据"""
        try:
            data = self.data_loader.load_data(self.data_path)
            processed_data = self.data_loader.preprocess_data(
                data,
                features=TECHNICAL_FACTORS,
                target=TARGET_VARIABLE
            )
            
            self.logger.info(f"交易数据加载完成: {len(processed_data)} 条记录")
            return processed_data
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {str(e)}")
            # 创建模拟数据
            return self._create_mock_trading_data()
    
    def _create_mock_trading_data(self) -> pd.DataFrame:
        """创建模拟交易数据"""
        self.logger.info("创建模拟交易数据...")
        
        np.random.seed(42)
        n_days = 100
        n_stocks = 2
        
        data = []
        base_date = pd.to_datetime('2024-01-01')
        
        for stock_id in range(n_stocks):
            price = 10.0
            
            for day in range(n_days):
                price_change = np.random.normal(0, 0.02)
                price = max(price * (1 + price_change), 1.0)
                
                current_date = base_date + pd.Timedelta(days=day)
                
                row = {
                    'ts_code': f'stock_{stock_id:03d}',
                    'trade_date': current_date.strftime('%Y-%m-%d'),
                    'close': price,
                    'open': price * (1 + np.random.normal(0, 0.01)),
                    'high': price * (1 + abs(np.random.normal(0, 0.015))),
                    'low': price * (1 - abs(np.random.normal(0, 0.015))),
                    'vol': np.random.lognormal(10, 1),
                    'amount': price * np.random.lognormal(10, 1),
                    'pct_chg': price_change * 100
                }
                
                # 添加技术指标
                for factor in TECHNICAL_FACTORS:
                    if factor not in row:
                        row[factor] = np.random.normal(0, 1)
                
                row[TARGET_VARIABLE] = np.random.normal(0, 5)
                data.append(row)
        
        return pd.DataFrame(data)
    
    def _load_model(self) -> MultiAgentDQN:
        """加载多智能体模型"""
        try:
            # 创建多智能体系统
            multi_agent = MultiAgentDQN(
                n_agents=N_AGENTS,
                state_dim=self.env.observation_space.shape[0],
                action_dim=self.env.action_space.shape[0],
                coordination_method='attention'
            )
            
            # 加载模型权重
            multi_agent.load(self.model_path)
            
            self.logger.info(f"模型加载成功: {self.model_path}")
            return multi_agent
            
        except Exception as e:
            self.logger.warning(f"模型加载失败: {str(e)}")
            self.logger.info("使用随机初始化的模型")
            
            # 返回随机初始化的模型
            return MultiAgentDQN(
                n_agents=N_AGENTS,
                state_dim=self.env.observation_space.shape[0],
                action_dim=self.env.action_space.shape[0],
                coordination_method='attention'
            )
    
    def _load_feature_fusion_model(self) -> MultiModalFeatureFusion:
        """加载特征融合模型"""
        try:
            feature_fusion = MultiModalFeatureFusion(
                input_dim=len(TECHNICAL_FACTORS),
                output_dim=256
            ).to(DEVICE)
            
            # 尝试加载特征融合模型权重
            fusion_path = self.model_path.replace('.pth', '_fusion.pth')
            if os.path.exists(fusion_path):
                checkpoint = torch.load(fusion_path, map_location=DEVICE)
                feature_fusion.load_state_dict(checkpoint['model_state_dict'])
                self.logger.info(f"特征融合模型加载成功: {fusion_path}")
            else:
                self.logger.warning("特征融合模型文件不存在，使用随机初始化")
            
            return feature_fusion
            
        except Exception as e:
            self.logger.warning(f"特征融合模型加载失败: {str(e)}")
            return MultiModalFeatureFusion(
                input_dim=len(TECHNICAL_FACTORS),
                output_dim=256
            ).to(DEVICE)
    
    def _extract_enhanced_features(self, state: np.ndarray) -> np.ndarray:
        """提取增强特征"""
        # 将状态转换为时间序列格式
        n_features = len(TECHNICAL_FACTORS)
        seq_len = LOOKBACK_WINDOW
        
        # 提取技术指标部分
        tech_features = state[:n_features * seq_len]
        tech_features = tech_features.reshape(seq_len, n_features)
        
        # 转换为张量
        tech_tensor = torch.FloatTensor(tech_features).unsqueeze(0).to(DEVICE)
        
        # 通过多模态特征融合
        with torch.no_grad():
            fused_features, _ = self.feature_fusion(tech_tensor)
            fused_features = fused_features.cpu().numpy().flatten()
        
        # 拼接投资组合状态
        portfolio_features = state[n_features * seq_len:]
        
        # 组合增强特征
        enhanced_state = np.concatenate([fused_features, portfolio_features])
        
        return enhanced_state
    
    def run_trading_simulation(self, 
                             start_date: str = None,
                             end_date: str = None) -> dict:
        """
        运行交易模拟
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            交易结果字典
        """
        self.logger.info("开始交易模拟...")
        
        # 过滤数据
        if start_date and end_date:
            trading_data = self.data_loader.filter_by_date(
                self.data, start_date, end_date
            )
            # 重新创建环境
            self.env = StockTradingEnv(trading_data, initial_capital=self.initial_capital)
        
        # 重置环境
        state = self.env.reset()
        total_reward = 0.0
        step_count = 0
        
        # 交易循环
        done = False
        while not done:
            # 提取增强特征
            enhanced_state = self._extract_enhanced_features(state)
            
            # 智能体决策
            action = self.multi_agent.select_action(enhanced_state, training=False)
            
            # 执行动作
            next_state, reward, done, info = self.env.step(action)
            
            # 记录交易信息
            self._log_trading_step(step_count, state, action, reward, info)
            
            # 更新状态
            state = next_state
            total_reward += reward
            step_count += 1
            
            # 记录投资组合价值
            self.portfolio_history.append(info['portfolio_value'])
        
        # 获取最终统计
        final_stats = self.env.get_portfolio_stats()
        
        # 计算性能指标
        performance_metrics = self.metrics_calculator.calculate_all_metrics(
            np.array(self.portfolio_history)
        )
        
        # 生成交易报告
        trading_report = {
            'initial_capital': self.initial_capital,
            'final_portfolio_value': self.portfolio_history[-1] if self.portfolio_history else self.initial_capital,
            'total_return': final_stats.get('total_return', 0),
            'total_reward': total_reward,
            'total_steps': step_count,
            'total_trades': len(self.trading_log),
            'performance_metrics': performance_metrics,
            'final_stats': final_stats
        }
        
        self.logger.info("交易模拟完成")
        self.logger.info(f"总收益率: {trading_report['total_return']:.4f}")
        self.logger.info(f"最终投资组合价值: {trading_report['final_portfolio_value']:.2f}")
        
        return trading_report
    
    def _log_trading_step(self, step: int, state: np.ndarray, action: np.ndarray, 
                         reward: float, info: dict):
        """记录交易步骤"""
        log_entry = {
            'step': step,
            'timestamp': datetime.now().isoformat(),
            'portfolio_value': info['portfolio_value'],
            'cash': info['cash'],
            'positions': info['positions'],
            'action': action.tolist(),
            'reward': reward,
            'trades': info.get('trades', []),
            'transaction_costs': info.get('transaction_costs', 0)
        }
        
        self.trading_log.append(log_entry)
    
    def save_trading_results(self, save_path: str):
        """保存交易结果"""
        results = {
            'trading_log': self.trading_log,
            'portfolio_history': self.portfolio_history,
            'model_path': self.model_path,
            'data_path': self.data_path,
            'initial_capital': self.initial_capital
        }
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"交易结果已保存到: {save_path}")
    
    def plot_trading_results(self, save_path: str = None):
        """绘制交易结果"""
        if not self.portfolio_history:
            self.logger.warning("没有交易历史数据可绘制")
            return
        
        import matplotlib.pyplot as plt
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 投资组合价值曲线
        axes[0, 0].plot(self.portfolio_history, color=COLORS['profit'])
        axes[0, 0].axhline(y=self.initial_capital, color=COLORS['neutral'], linestyle='--', alpha=0.7)
        axes[0, 0].set_title('Portfolio Value')
        axes[0, 0].set_xlabel('Time Steps')
        axes[0, 0].set_ylabel('Value')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 收益率分布
        if len(self.portfolio_history) > 1:
            returns = np.diff(self.portfolio_history) / self.portfolio_history[:-1]
            axes[0, 1].hist(returns, bins=30, alpha=0.7, color=COLORS['neutral'])
            axes[0, 1].set_title('Returns Distribution')
            axes[0, 1].set_xlabel('Returns')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 回撤曲线
        values = np.array(self.portfolio_history)
        peak = np.maximum.accumulate(values)
        drawdown = (peak - values) / peak
        axes[1, 0].fill_between(range(len(drawdown)), drawdown, 0, 
                               color=COLORS['loss'], alpha=0.7)
        axes[1, 0].set_title('Drawdown')
        axes[1, 0].set_xlabel('Time Steps')
        axes[1, 0].set_ylabel('Drawdown')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 交易次数统计
        trade_counts = [len(log['trades']) for log in self.trading_log]
        axes[1, 1].plot(trade_counts, color=COLORS['benchmark'])
        axes[1, 1].set_title('Trading Activity')
        axes[1, 1].set_xlabel('Time Steps')
        axes[1, 1].set_ylabel('Number of Trades')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=DPI, bbox_inches='tight')
            plt.close()
        else:
            plt.show()

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='深度强化学习股票交易')
    
    parser.add_argument('--model_path', type=str, required=True,
                       help='模型路径前缀')
    parser.add_argument('--data_path', type=str, 
                       default=STOCK_DATA_PATH,
                       help='数据文件路径')
    parser.add_argument('--start_date', type=str,
                       default=None,
                       help='交易开始日期')
    parser.add_argument('--end_date', type=str,
                       default=None,
                       help='交易结束日期')
    parser.add_argument('--initial_capital', type=float,
                       default=INITIAL_CAPITAL,
                       help='初始资金')
    parser.add_argument('--output_dir', type=str,
                       default=RESULT_DIR,
                       help='结果输出目录')
    parser.add_argument('--save_results', action='store_true',
                       help='保存交易结果')
    parser.add_argument('--plot_results', action='store_true',
                       help='绘制交易结果')
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    print("深度强化学习股票交易系统")
    print("=" * 50)
    print(f"模型路径: {args.model_path}")
    print(f"数据路径: {args.data_path}")
    print(f"初始资金: {args.initial_capital:,.2f}")
    
    try:
        # 创建交易器
        trader = RLStockTrader(
            model_path=args.model_path,
            data_path=args.data_path,
            initial_capital=args.initial_capital
        )
        
        # 运行交易模拟
        trading_report = trader.run_trading_simulation(
            start_date=args.start_date,
            end_date=args.end_date
        )
        
        # 打印结果
        print("\n" + "=" * 50)
        print("交易结果:")
        print("=" * 50)
        print(f"初始资金: {trading_report['initial_capital']:,.2f}")
        print(f"最终价值: {trading_report['final_portfolio_value']:,.2f}")
        print(f"总收益率: {trading_report['total_return']:.4f} ({trading_report['total_return']*100:.2f}%)")
        print(f"总奖励: {trading_report['total_reward']:.4f}")
        print(f"交易步数: {trading_report['total_steps']}")
        print(f"交易次数: {trading_report['total_trades']}")
        
        # 打印性能指标
        metrics = trading_report['performance_metrics']
        if metrics:
            print("\n性能指标:")
            print(f"  年化收益率: {metrics.get('annualized_return', 0):.4f}")
            print(f"  波动率: {metrics.get('volatility', 0):.4f}")
            print(f"  夏普比率: {metrics.get('sharpe_ratio', 0):.4f}")
            print(f"  最大回撤: {metrics.get('max_drawdown', 0):.4f}")
            print(f"  胜率: {metrics.get('win_rate', 0):.4f}")
        
        # 保存结果
        if args.save_results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_path = os.path.join(args.output_dir, f"trading_results_{timestamp}.json")
            trader.save_trading_results(results_path)
        
        # 绘制结果
        if args.plot_results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_path = os.path.join(args.output_dir, f"trading_plot_{timestamp}.png")
            trader.plot_trading_results(plot_path)
        
        print("\n交易完成！")
        
    except Exception as e:
        print(f"交易过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
