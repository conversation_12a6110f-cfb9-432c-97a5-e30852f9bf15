# -*- coding: utf-8 -*-
"""
深度强化学习股票预测系统配置文件
整合DQN多智能体和动态分块序列建模的配置参数
"""

import torch
import os
from datetime import datetime

# ==================== 基础配置 ====================
# 设备配置
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
SEED = 42

# 数据路径配置
DATA_DIR = "data"
MODEL_DIR = "checkpoints"
LOG_DIR = "logs"
RESULT_DIR = "results"

# 确保目录存在
for dir_path in [DATA_DIR, MODEL_DIR, LOG_DIR, RESULT_DIR]:
    os.makedirs(dir_path, exist_ok=True)

# ==================== 数据配置 ====================
# 股票数据配置
STOCK_DATA_PATH = "../tushare_data_cyb/stock_factors_cyb_train.csv"
LOOKBACK_WINDOW = 20  # 历史数据窗口
PREDICTION_HORIZON = 3  # 预测未来3天

# 特征配置 - 继承现有系统的技术因子
TECHNICAL_FACTORS = [
    'price_acceleration', 'rsi_acceleration', 'price_velocity_factor', 'ma_ratio_5d',
    'volatility_regime', 'close_position', 'pct_chg', 'information_ratio',
    'rsi_divergence', 'range_expansion', 'momentum_5d', 'volatility_5d',
    'size_factor', 'pb_relative', 'downside_risk_20d', 'kdj_j',
    'dynamic_support_resistance', 'skew_20d', 'volatility_adjusted_momentum_10d', 'kurt_20d',
    'amount', 'reaction_speed_factor', 'plus_di', 'stealth_accumulation',
    'natr', 'kdj_d', 'normalized_composite_momentum', 'volatility_10d',
    'liquidity_consumption', 'exhaustion_indicator', 'minus_di', 'momentum_10d',
    'sentiment_transition', 'pe_relative', 'ma_ratio_10d', 'fear_index_proxy',
    'quote_stability', 'micro_momentum', 'market_depth_proxy', 'strength_persistence',
    'intraday_volatility_distribution', 'price_discovery_efficiency', 'mfi_14d', 'smart_money',
    'sentiment_momentum', 'real_body_ratio', 'obv', 'price_jump_detection',
    'bb_ratio', 'volume_ratio', 'lower_shadow', 'trend_strength_20d',
    'volume_weighted_momentum_10d', 'volatility_20d', 'adx', 'extreme_sentiment',
    'kdj_k', 'volume_price_trend', 'volatility_adjusted_momentum_20d', 'trend_strength_10d',
    'rsi', 'macd_hist', 'upper_shadow', 'greed_index_proxy',
]

# 目标变量
TARGET_VARIABLE = "target_return_close"  # (day 3 high price / day 2 open price - 1) * 100

# ==================== DQN智能体配置 ====================
# 智能体基础配置
N_AGENTS = 3  # 多智能体数量
ACTION_SPACE_SIZE = 3  # 动作空间：0=卖出, 1=持有, 2=买入
STATE_DIM = len(TECHNICAL_FACTORS) * LOOKBACK_WINDOW  # 状态维度

# DQN网络配置
DQN_HIDDEN_DIMS = [512, 256, 128]  # 隐藏层维度
DQN_DROPOUT = 0.2
DQN_ACTIVATION = 'relu'

# 训练配置
BATCH_SIZE = 64
LEARNING_RATE = 1e-4
GAMMA = 0.99  # 折扣因子
TAU = 0.005   # 软更新参数
EPSILON_START = 1.0
EPSILON_END = 0.01
EPSILON_DECAY = 0.995
TARGET_UPDATE_FREQ = 100  # 目标网络更新频率

# 经验回放配置
REPLAY_BUFFER_SIZE = 100000
MIN_REPLAY_SIZE = 1000
PRIORITY_ALPHA = 0.6  # 优先经验回放参数
PRIORITY_BETA_START = 0.4
PRIORITY_BETA_FRAMES = 100000

# ==================== 动态分块配置 ====================
# 分块参数
MIN_CHUNK_SIZE = 5    # 最小分块大小
MAX_CHUNK_SIZE = 15   # 最大分块大小
CHUNK_EMBEDDING_DIM = 64  # 分块嵌入维度

# 分层建模配置
N_HIERARCHY_LEVELS = 3  # 分层级别数
HIERARCHY_DIMS = [128, 64, 32]  # 各层维度

# 注意力机制配置
ATTENTION_HEADS = 8
ATTENTION_DROPOUT = 0.1

# ==================== 多模态特征融合配置 ====================
# CNN特征提取配置
CNN_CHANNELS = [32, 64, 128]
CNN_KERNEL_SIZES = [3, 5, 7]
CNN_DROPOUT = 0.2

# LSTM配置
LSTM_HIDDEN_SIZE = 128
LSTM_NUM_LAYERS = 2
LSTM_DROPOUT = 0.2

# Transformer配置
TRANSFORMER_D_MODEL = 128
TRANSFORMER_NHEAD = 8
TRANSFORMER_NUM_LAYERS = 4
TRANSFORMER_DROPOUT = 0.1

# ==================== 交易环境配置 ====================
# 交易参数
INITIAL_CAPITAL = 1000000  # 初始资金100万
TRANSACTION_COST = 0.001   # 交易成本0.1%
MAX_POSITION_SIZE = 0.1    # 最大单只股票仓位10%
RISK_FREE_RATE = 0.03      # 无风险利率3%

# 奖励函数配置
REWARD_SCALING = 1.0
RISK_PENALTY = 0.1
TRANSACTION_PENALTY = 0.01

# ==================== 训练配置 ====================
# 训练参数
N_EPISODES = 1000
MAX_STEPS_PER_EPISODE = 252  # 一年交易日
EVAL_FREQUENCY = 50  # 评估频率
SAVE_FREQUENCY = 100  # 保存频率

# 早停配置
EARLY_STOPPING_PATIENCE = 100
MIN_IMPROVEMENT = 0.001

# 学习率调度
LR_SCHEDULER = 'cosine'
LR_WARMUP_STEPS = 1000
LR_DECAY_STEPS = 10000

# ==================== 评估配置 ====================
# 评估指标
EVAL_METRICS = [
    'total_return',
    'sharpe_ratio', 
    'max_drawdown',
    'win_rate',
    'profit_loss_ratio',
    'volatility',
    'calmar_ratio'
]

# 回测配置
BACKTEST_START_DATE = '2020-01-01'
BACKTEST_END_DATE = '2024-12-31'
BENCHMARK = '000300.SH'  # 沪深300作为基准

# ==================== 日志配置 ====================
# 日志级别
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# TensorBoard配置
TENSORBOARD_LOG_DIR = os.path.join(LOG_DIR, 'tensorboard')
TENSORBOARD_UPDATE_FREQ = 100

# ==================== 模型保存配置 ====================
# 检查点配置
CHECKPOINT_SAVE_BEST = True
CHECKPOINT_SAVE_LAST = True
CHECKPOINT_SAVE_FREQ = 100

# 模型导出配置
EXPORT_ONNX = False
EXPORT_TORCHSCRIPT = True

# ==================== 可视化配置 ====================
# 图表配置
FIGURE_SIZE = (12, 8)
DPI = 300
STYLE = 'seaborn-v0_8'

# 颜色配置
COLORS = {
    'profit': '#2E8B57',
    'loss': '#DC143C', 
    'neutral': '#4682B4',
    'benchmark': '#FF8C00'
}

# ==================== 实用函数 ====================
def get_run_id():
    """生成运行ID"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def get_model_path(run_id, model_type='best'):
    """获取模型路径"""
    return os.path.join(MODEL_DIR, f"{run_id}_{model_type}.pth")

def get_log_path(run_id):
    """获取日志路径"""
    return os.path.join(LOG_DIR, f"{run_id}.log")

def get_result_path(run_id):
    """获取结果路径"""
    return os.path.join(RESULT_DIR, f"{run_id}_results.json")

# ==================== 验证配置 ====================
def validate_config():
    """验证配置参数的合理性"""
    assert LOOKBACK_WINDOW > 0, "LOOKBACK_WINDOW must be positive"
    assert PREDICTION_HORIZON > 0, "PREDICTION_HORIZON must be positive"
    assert N_AGENTS > 0, "N_AGENTS must be positive"
    assert ACTION_SPACE_SIZE > 0, "ACTION_SPACE_SIZE must be positive"
    assert 0 < GAMMA < 1, "GAMMA must be between 0 and 1"
    assert 0 < TAU < 1, "TAU must be between 0 and 1"
    assert EPSILON_START >= EPSILON_END, "EPSILON_START must be >= EPSILON_END"
    assert INITIAL_CAPITAL > 0, "INITIAL_CAPITAL must be positive"
    assert 0 <= TRANSACTION_COST < 1, "TRANSACTION_COST must be between 0 and 1"
    assert 0 < MAX_POSITION_SIZE <= 1, "MAX_POSITION_SIZE must be between 0 and 1"
    
    print("✓ 配置验证通过")

if __name__ == "__main__":
    validate_config()
    print("配置文件加载成功")
