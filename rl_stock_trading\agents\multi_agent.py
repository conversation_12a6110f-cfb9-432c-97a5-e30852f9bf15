# -*- coding: utf-8 -*-
"""
多智能体协调器实现
基于DQN的多智能体强化学习系统，用于股票交易决策
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Dict, Tuple, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *
from agents.dqn_agent import DQNAgent

class AttentionCoordinator(nn.Module):
    """
    基于注意力机制的智能体协调器
    用于融合多个智能体的决策
    """
    def __init__(self, 
                 n_agents: int = N_AGENTS,
                 action_dim: int = ACTION_SPACE_SIZE,
                 hidden_dim: int = 128):
        super(AttentionCoordinator, self).__init__()
        
        self.n_agents = n_agents
        self.action_dim = action_dim
        self.hidden_dim = hidden_dim
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=action_dim,
            num_heads=4,
            dropout=0.1,
            batch_first=True
        )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(action_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, action_dim),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, agent_q_values: torch.Tensor) -> torch.Tensor:
        """
        协调多个智能体的Q值
        
        Args:
            agent_q_values: [batch_size, n_agents, action_dim]
            
        Returns:
            融合后的Q值 [batch_size, action_dim]
        """
        batch_size = agent_q_values.size(0)
        
        # 注意力机制融合
        attended_values, attention_weights = self.attention(
            agent_q_values, agent_q_values, agent_q_values
        )
        
        # 加权平均
        weighted_q_values = attended_values.mean(dim=1)  # [batch_size, action_dim]
        
        # 通过融合网络
        final_q_values = self.fusion_network(weighted_q_values)
        
        return final_q_values

class MultiAgentDQN:
    """
    多智能体DQN系统
    协调多个DQN智能体进行股票交易决策
    """
    def __init__(self, 
                 n_agents: int = N_AGENTS,
                 state_dim: int = STATE_DIM,
                 action_dim: int = ACTION_SPACE_SIZE,
                 coordination_method: str = 'attention'):
        """
        初始化多智能体系统
        
        Args:
            n_agents: 智能体数量
            state_dim: 状态维度
            action_dim: 动作维度
            coordination_method: 协调方法 ('attention', 'voting', 'weighted')
        """
        self.n_agents = n_agents
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.coordination_method = coordination_method
        
        # 创建多个DQN智能体
        self.agents = []
        for i in range(n_agents):
            agent = DQNAgent(
                agent_id=i,
                state_dim=state_dim,
                action_dim=action_dim,
                # 为不同智能体设置不同的超参数
                epsilon_decay=EPSILON_DECAY * (0.9 + 0.1 * i),  # 不同的探索策略
                learning_rate=LEARNING_RATE * (0.8 + 0.4 * i)   # 不同的学习率
            )
            self.agents.append(agent)
        
        # 协调器
        if coordination_method == 'attention':
            self.coordinator = AttentionCoordinator(n_agents, action_dim).to(DEVICE)
            self.coordinator_optimizer = torch.optim.Adam(
                self.coordinator.parameters(), lr=LEARNING_RATE
            )
        else:
            self.coordinator = None
        
        # 智能体权重（用于加权投票）
        self.agent_weights = np.ones(n_agents) / n_agents
        
        # 训练统计
        self.coordination_losses = []
        self.agent_performances = np.zeros(n_agents)
    
    def select_action(self, state: np.ndarray, training: bool = True) -> np.ndarray:
        """
        多智能体协调选择动作
        
        Args:
            state: 当前状态
            training: 是否为训练模式
            
        Returns:
            选择的动作
        """
        if self.coordination_method == 'attention':
            return self._attention_based_action(state, training)
        elif self.coordination_method == 'voting':
            return self._voting_based_action(state, training)
        elif self.coordination_method == 'weighted':
            return self._weighted_action(state, training)
        else:
            raise ValueError(f"Unknown coordination method: {self.coordination_method}")
    
    def _attention_based_action(self, state: np.ndarray, training: bool) -> np.ndarray:
        """基于注意力机制的动作选择"""
        # 获取所有智能体的Q值
        agent_q_values = []
        for agent in self.agents:
            q_values = agent.get_q_values(state)
            agent_q_values.append(q_values)

        # 堆叠Q值 [1, n_agents, action_dim]
        agent_q_values = torch.stack(agent_q_values, dim=1)

        # 通过协调器融合
        with torch.no_grad():
            final_q_values = self.coordinator(agent_q_values)
            # 为每只股票生成动作
            actions = []
            for i in range(len(self.agents)):  # 假设每个智能体对应一只股票
                if i < final_q_values.size(-1):
                    # 使用softmax采样或贪婪选择
                    if training and np.random.random() < 0.1:  # 10%探索
                        action = np.random.uniform(-1, 1)
                    else:
                        # 将Q值映射到[-1, 1]范围
                        q_val = final_q_values[0, i % final_q_values.size(-1)].item()
                        action = np.tanh(q_val)  # 映射到[-1, 1]
                else:
                    action = 0.0  # 持有
                actions.append(action)

        return np.array(actions, dtype=np.float32)
    
    def _voting_based_action(self, state: np.ndarray, training: bool) -> np.ndarray:
        """基于投票的动作选择"""
        # 为每只股票生成动作
        actions = []

        for i in range(self.action_dim):
            votes = np.zeros(3)  # 卖出、持有、买入

            for agent in self.agents:
                agent_action = agent.select_action(state, training)
                # 将连续动作转换为离散投票
                if agent_action > 0.33:
                    votes[2] += 1  # 买入
                elif agent_action < -0.33:
                    votes[0] += 1  # 卖出
                else:
                    votes[1] += 1  # 持有

            # 根据投票结果生成动作
            winner = np.argmax(votes)
            if winner == 2:
                action = 0.5  # 买入
            elif winner == 0:
                action = -0.5  # 卖出
            else:
                action = 0.0  # 持有

            actions.append(action)

        return np.array(actions, dtype=np.float32)
    
    def _weighted_action(self, state: np.ndarray, training: bool) -> np.ndarray:
        """基于加权的动作选择"""
        # 为每只股票生成动作
        actions = []

        for stock_idx in range(self.action_dim):
            weighted_q_values = np.zeros(3)  # 假设3个动作类别

            for i, agent in enumerate(self.agents):
                q_values = agent.get_q_values(state).cpu().numpy().flatten()
                # 使用Q值的加权平均
                if len(q_values) >= 3:
                    weighted_q_values += self.agent_weights[i] * q_values[:3]

            # 将Q值转换为连续动作
            best_action_idx = np.argmax(weighted_q_values)
            if best_action_idx == 2:
                action = 0.5  # 买入
            elif best_action_idx == 0:
                action = -0.5  # 卖出
            else:
                action = 0.0  # 持有

            actions.append(action)

        return np.array(actions, dtype=np.float32)
    
    def store_experience(self, state: np.ndarray, action: np.ndarray, reward: float,
                        next_state: np.ndarray, done: bool):
        """
        为所有智能体存储经验

        Args:
            state: 当前状态
            action: 执行的动作数组
            reward: 获得的奖励
            next_state: 下一状态
            done: 是否结束
        """
        # 将连续动作转换为离散动作索引供DQN使用
        for i, agent in enumerate(self.agents):
            # 为每个智能体分配一个动作
            if i < len(action):
                # 将连续动作[-1, 1]转换为离散动作索引[0, 1, 2]
                continuous_action = action[i]
                if continuous_action > 0.33:
                    discrete_action = 2  # 买入
                elif continuous_action < -0.33:
                    discrete_action = 0  # 卖出
                else:
                    discrete_action = 1  # 持有
            else:
                discrete_action = 1  # 默认持有

            agent.store_experience(state, discrete_action, reward, next_state, done)
    
    def train(self, batch_size: int = BATCH_SIZE) -> Dict[str, float]:
        """
        训练所有智能体
        
        Args:
            batch_size: 批次大小
            
        Returns:
            训练损失字典
        """
        losses = {}
        
        # 训练各个智能体
        for i, agent in enumerate(self.agents):
            loss = agent.train(batch_size)
            if loss is not None:
                losses[f'agent_{i}_loss'] = loss
        
        # 训练协调器（如果使用注意力机制）
        if self.coordination_method == 'attention' and len(self.agents[0].replay_buffer) >= MIN_REPLAY_SIZE:
            coord_loss = self._train_coordinator(batch_size)
            if coord_loss is not None:
                losses['coordinator_loss'] = coord_loss
        
        return losses
    
    def _train_coordinator(self, batch_size: int) -> Optional[float]:
        """训练协调器"""
        # 从第一个智能体采样经验（假设所有智能体共享经验）
        if self.agents[0].use_prioritized_replay:
            experiences, _, _ = self.agents[0].replay_buffer.sample(batch_size)
        else:
            experiences = self.agents[0].replay_buffer.sample(batch_size)
        
        from agents.replay_buffer import collate_experiences
        states, actions, rewards, next_states, dones = collate_experiences(experiences)
        
        # 获取所有智能体的Q值
        agent_q_values = []
        for agent in self.agents:
            with torch.no_grad():
                q_values = agent.q_network(states)
                agent_q_values.append(q_values)
        
        agent_q_values = torch.stack(agent_q_values, dim=1)  # [batch_size, n_agents, action_dim]
        
        # 通过协调器
        coordinated_q_values = self.coordinator(agent_q_values)
        
        # 计算目标Q值（使用最佳智能体的目标网络）
        best_agent = self.agents[np.argmax(self.agent_performances)]
        with torch.no_grad():
            next_q_values = best_agent.target_network(next_states).max(1)[0]
            target_q_values = rewards + (GAMMA * next_q_values * (~dones))
        
        # 计算损失
        current_q_values = coordinated_q_values.gather(1, actions.unsqueeze(1)).squeeze()
        loss = nn.MSELoss()(current_q_values, target_q_values)
        
        # 反向传播
        self.coordinator_optimizer.zero_grad()
        loss.backward()
        self.coordinator_optimizer.step()
        
        loss_value = loss.item()
        self.coordination_losses.append(loss_value)
        
        return loss_value
    
    def update_agent_weights(self, performances: np.ndarray):
        """
        根据性能更新智能体权重
        
        Args:
            performances: 各智能体的性能指标
        """
        self.agent_performances = performances
        
        # 使用softmax更新权重
        exp_performances = np.exp(performances - np.max(performances))
        self.agent_weights = exp_performances / np.sum(exp_performances)
    
    def get_training_stats(self) -> Dict:
        """获取训练统计信息"""
        stats = {
            'coordination_method': self.coordination_method,
            'agent_weights': self.agent_weights.tolist(),
            'agent_performances': self.agent_performances.tolist(),
            'agents': []
        }
        
        for agent in self.agents:
            stats['agents'].append(agent.get_training_stats())
        
        if self.coordination_losses:
            stats['avg_coordination_loss'] = np.mean(self.coordination_losses[-100:])
        
        return stats
    
    def save(self, filepath_prefix: str):
        """保存所有智能体和协调器"""
        for i, agent in enumerate(self.agents):
            agent.save(f"{filepath_prefix}_agent_{i}.pth")
        
        if self.coordinator is not None:
            torch.save({
                'coordinator_state_dict': self.coordinator.state_dict(),
                'coordinator_optimizer_state_dict': self.coordinator_optimizer.state_dict(),
                'agent_weights': self.agent_weights,
                'agent_performances': self.agent_performances
            }, f"{filepath_prefix}_coordinator.pth")
    
    def load(self, filepath_prefix: str):
        """加载所有智能体和协调器"""
        for i, agent in enumerate(self.agents):
            agent.load(f"{filepath_prefix}_agent_{i}.pth")
        
        if self.coordinator is not None:
            checkpoint = torch.load(f"{filepath_prefix}_coordinator.pth", map_location=DEVICE)
            self.coordinator.load_state_dict(checkpoint['coordinator_state_dict'])
            self.coordinator_optimizer.load_state_dict(checkpoint['coordinator_optimizer_state_dict'])
            self.agent_weights = checkpoint['agent_weights']
            self.agent_performances = checkpoint['agent_performances']

if __name__ == "__main__":
    # 测试代码
    print("测试多智能体DQN系统...")
    
    multi_agent = MultiAgentDQN(n_agents=3, coordination_method='attention')
    
    # 测试动作选择
    state = np.random.randn(STATE_DIM)
    action = multi_agent.select_action(state)
    print(f"多智能体选择的动作: {action}")
    
    # 测试经验存储
    next_state = np.random.randn(STATE_DIM)
    multi_agent.store_experience(state, action, 1.0, next_state, False)
    
    # 测试训练统计
    stats = multi_agent.get_training_stats()
    print(f"智能体数量: {len(stats['agents'])}")
    print("✓ 多智能体DQN系统测试通过")
