#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试维度匹配问题
"""

import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append('rl_stock_trading')
sys.path.append(os.path.join(os.path.dirname(__file__), 'rl_stock_trading'))

from rl_stock_trading.utils.config import *
from rl_stock_trading.models.multimodal.feature_fusion import MultiModalFeatureFusion
from rl_stock_trading.models.dynamic_chunking.chunking_module import DynamicChunkingModel

def test_dynamic_chunking():
    """测试动态分块模块"""
    print("测试动态分块模块...")
    
    batch_size = 2
    seq_len = LOOKBACK_WINDOW
    input_dim = len(TECHNICAL_FACTORS)
    output_dim = 128
    
    # 创建测试数据
    x = torch.randn(batch_size, seq_len, input_dim)
    print(f"输入形状: {x.shape}")
    
    # 创建模型
    model = DynamicChunkingModel(input_dim, output_dim)
    
    try:
        # 前向传播
        output, aux_outputs = model(x)
        print(f"动态分块输出形状: {output.shape}")
        print(f"分块边界形状: {aux_outputs['chunk_boundaries'].shape}")
        print("✓ 动态分块模块测试通过")
        return True
    except Exception as e:
        print(f"✗ 动态分块模块测试失败: {str(e)}")
        return False

def test_multimodal_fusion():
    """测试多模态特征融合"""
    print("\n测试多模态特征融合...")
    
    batch_size = 2
    seq_len = LOOKBACK_WINDOW
    input_dim = len(TECHNICAL_FACTORS)
    output_dim = 256
    
    # 创建测试数据
    x = torch.randn(batch_size, seq_len, input_dim)
    print(f"输入形状: {x.shape}")
    
    # 创建模型
    model = MultiModalFeatureFusion(input_dim, output_dim)
    
    try:
        # 前向传播
        fused_features, aux_outputs = model(x)
        print(f"融合特征形状: {fused_features.shape}")
        print(f"门控权重形状: {aux_outputs['gate_weights'].shape}")
        print("✓ 多模态特征融合测试通过")
        return True
    except Exception as e:
        print(f"✗ 多模态特征融合测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_processor():
    """测试特征处理器"""
    print("\n测试特征处理器...")
    
    from rl_stock_trading.utils.feature_utils import FeatureProcessor
    
    # 创建模拟状态
    n_features = len(TECHNICAL_FACTORS)
    seq_len = LOOKBACK_WINDOW
    portfolio_features = 5  # 假设5个投资组合特征
    
    state = np.random.randn(n_features * seq_len + portfolio_features)
    print(f"状态形状: {state.shape}")
    
    # 创建特征处理器
    processor = FeatureProcessor()
    
    try:
        # 测试特征提取
        enhanced_features = processor.extract_enhanced_features(state)
        print(f"增强特征形状: {enhanced_features.shape}")
        print("✓ 特征处理器测试通过")
        return True
    except Exception as e:
        print(f"✗ 特征处理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始维度测试...")
    print("=" * 50)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 运行测试
    tests = [
        test_dynamic_chunking,
        test_multimodal_fusion,
        test_feature_processor
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 发生异常: {str(e)}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！")
        return True
    else:
        print("✗ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
