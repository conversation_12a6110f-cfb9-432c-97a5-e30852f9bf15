# -*- coding: utf-8 -*-
"""
深度强化学习训练器
整合DQN多智能体、动态分块和多模态特征融合的训练流程
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging
import time
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from tensorboardX import SummaryWriter
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import *
from agents.multi_agent import MultiAgentDQN
from models.multimodal.feature_fusion import MultiModalFeatureFusion
from environment.trading_env import StockTradingEnv
from training.evaluator import PerformanceEvaluator
from utils.feature_utils import get_feature_processor

class RLStockTrainer:
    """
    强化学习股票交易训练器
    """
    
    def __init__(self, 
                 train_data: pd.DataFrame,
                 val_data: pd.DataFrame,
                 run_id: str = None):
        """
        初始化训练器
        
        Args:
            train_data: 训练数据
            val_data: 验证数据
            run_id: 运行ID
        """
        self.train_data = train_data
        self.val_data = val_data
        self.run_id = run_id or get_run_id()
        
        # 设置日志
        self._setup_logging()
        
        # 创建环境
        self.train_env = StockTradingEnv(train_data)
        self.val_env = StockTradingEnv(val_data)
        
        # 计算增强特征的维度
        # 多模态特征融合输出256维 + 投资组合特征
        portfolio_feature_dim = len(self.train_env.stock_codes) + 3  # 各股票仓位 + 现金比例 + 总价值 + 日收益率
        enhanced_state_dim = 256 + portfolio_feature_dim  # 多模态特征 + 投资组合特征

        # 创建多智能体系统
        self.multi_agent = MultiAgentDQN(
            n_agents=N_AGENTS,
            state_dim=enhanced_state_dim,
            action_dim=self.train_env.action_space.shape[0],
            coordination_method='attention'
        )
        
        # 创建多模态特征融合模型
        self.feature_fusion = MultiModalFeatureFusion(
            input_dim=len(TECHNICAL_FACTORS),
            output_dim=256
        ).to(DEVICE)
        
        # 特征融合优化器
        self.fusion_optimizer = torch.optim.Adam(
            self.feature_fusion.parameters(), 
            lr=LEARNING_RATE
        )
        
        # 评估器
        self.evaluator = PerformanceEvaluator()

        # 特征处理器
        self.feature_processor = get_feature_processor(self.feature_fusion)
        
        # TensorBoard
        self.writer = SummaryWriter(
            log_dir=os.path.join(TENSORBOARD_LOG_DIR, self.run_id)
        )
        
        # 训练统计
        self.training_stats = {
            'episode_rewards': [],
            'episode_lengths': [],
            'portfolio_values': [],
            'losses': [],
            'evaluation_scores': []
        }
        
        # 最佳模型跟踪
        self.best_score = -np.inf
        self.best_episode = 0
        
        self.logger.info(f"训练器初始化完成，运行ID: {self.run_id}")
    
    def _setup_logging(self):
        """设置日志"""
        log_path = get_log_path(self.run_id)
        
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL),
            format=LOG_FORMAT,
            handlers=[
                logging.FileHandler(log_path, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    def train(self, n_episodes: int = N_EPISODES) -> Dict:
        """
        训练主循环
        
        Args:
            n_episodes: 训练轮数
            
        Returns:
            训练结果字典
        """
        self.logger.info(f"开始训练，共 {n_episodes} 轮")
        
        start_time = time.time()
        
        for episode in tqdm(range(n_episodes), desc="训练进度"):
            # 训练一轮
            episode_stats = self._train_episode(episode)
            
            # 记录统计信息
            self._record_episode_stats(episode, episode_stats)
            
            # 定期评估
            if (episode + 1) % EVAL_FREQUENCY == 0:
                eval_score = self._evaluate(episode)
                self.training_stats['evaluation_scores'].append(eval_score)
                
                # 保存最佳模型
                if eval_score > self.best_score:
                    self.best_score = eval_score
                    self.best_episode = episode
                    self._save_best_model()
                    self.logger.info(f"新的最佳模型！评分: {eval_score:.4f}")
            
            # 定期保存检查点
            if (episode + 1) % SAVE_FREQUENCY == 0:
                self._save_checkpoint(episode)
            
            # 早停检查
            if self._should_early_stop(episode):
                self.logger.info(f"早停触发，在第 {episode} 轮停止训练")
                break
        
        # 训练完成
        total_time = time.time() - start_time
        self.logger.info(f"训练完成，耗时: {total_time:.2f}秒")
        
        # 最终评估
        final_score = self._evaluate(episode, final=True)
        
        # 保存最终模型
        self._save_final_model()
        
        # 生成训练报告
        training_report = self._generate_training_report(total_time, final_score)
        
        return training_report
    
    def _train_episode(self, episode: int) -> Dict:
        """
        训练一轮
        
        Args:
            episode: 当前轮数
            
        Returns:
            轮次统计信息
        """
        # 重置环境
        state = self.train_env.reset()
        
        episode_reward = 0.0
        episode_length = 0
        episode_losses = []
        
        done = False
        
        while not done:
            # 特征提取和融合
            enhanced_state = self._extract_enhanced_features(state)
            
            # 智能体选择动作
            action = self.multi_agent.select_action(enhanced_state, training=True)
            
            # 执行动作
            next_state, reward, done, info = self.train_env.step(action)
            
            # 存储经验
            next_enhanced_state = self._extract_enhanced_features(next_state)
            self.multi_agent.store_experience(
                enhanced_state, action, reward, next_enhanced_state, done
            )
            
            # 训练智能体
            if len(self.multi_agent.agents[0].replay_buffer) >= MIN_REPLAY_SIZE:
                losses = self.multi_agent.train(BATCH_SIZE)
                if losses:
                    episode_losses.extend(losses.values())
            
            # 更新状态
            state = next_state
            episode_reward += reward
            episode_length += 1
            
            # 防止无限循环
            if episode_length >= MAX_STEPS_PER_EPISODE:
                break
        
        # 轮次统计
        episode_stats = {
            'reward': episode_reward,
            'length': episode_length,
            'portfolio_value': info.get('portfolio_value', 0),
            'avg_loss': np.mean(episode_losses) if episode_losses else 0.0,
            'max_drawdown': info.get('max_drawdown', 0),
            'total_trades': len(info.get('trades', []))
        }
        
        return episode_stats
    
    def _extract_enhanced_features(self, state: np.ndarray) -> np.ndarray:
        """
        提取增强特征 - 使用公共特征处理器

        Args:
            state: 原始状态

        Returns:
            增强后的状态特征
        """
        return self.feature_processor.extract_enhanced_features(state)
    
    def _record_episode_stats(self, episode: int, stats: Dict):
        """记录轮次统计信息"""
        self.training_stats['episode_rewards'].append(stats['reward'])
        self.training_stats['episode_lengths'].append(stats['length'])
        self.training_stats['portfolio_values'].append(stats['portfolio_value'])
        self.training_stats['losses'].append(stats['avg_loss'])
        
        # TensorBoard记录
        self.writer.add_scalar('Episode/Reward', stats['reward'], episode)
        self.writer.add_scalar('Episode/Length', stats['length'], episode)
        self.writer.add_scalar('Episode/PortfolioValue', stats['portfolio_value'], episode)
        self.writer.add_scalar('Episode/AvgLoss', stats['avg_loss'], episode)
        self.writer.add_scalar('Episode/MaxDrawdown', stats['max_drawdown'], episode)
        self.writer.add_scalar('Episode/TotalTrades', stats['total_trades'], episode)
        
        # 记录智能体统计
        agent_stats = self.multi_agent.get_training_stats()
        for i, agent_stat in enumerate(agent_stats['agents']):
            self.writer.add_scalar(f'Agent_{i}/Epsilon', agent_stat['epsilon'], episode)
            self.writer.add_scalar(f'Agent_{i}/BufferSize', agent_stat['buffer_size'], episode)
            self.writer.add_scalar(f'Agent_{i}/AvgLoss', agent_stat['avg_loss'], episode)
        
        # 定期打印进度
        if (episode + 1) % 10 == 0:
            avg_reward = np.mean(self.training_stats['episode_rewards'][-10:])
            avg_value = np.mean(self.training_stats['portfolio_values'][-10:])
            
            self.logger.info(
                f"轮次 {episode+1}: 平均奖励={avg_reward:.4f}, "
                f"平均投资组合价值={avg_value:.2f}, "
                f"平均损失={stats['avg_loss']:.6f}"
            )
    
    def _evaluate(self, episode: int, final: bool = False) -> float:
        """
        评估模型性能
        
        Args:
            episode: 当前轮数
            final: 是否为最终评估
            
        Returns:
            评估分数
        """
        self.logger.info(f"开始评估模型 (轮次 {episode})")
        
        # 在验证环境中运行
        state = self.val_env.reset()
        total_reward = 0.0
        done = False
        
        while not done:
            enhanced_state = self._extract_enhanced_features(state)
            action = self.multi_agent.select_action(enhanced_state, training=False)
            state, reward, done, info = self.val_env.step(action)
            total_reward += reward
        
        # 获取投资组合统计
        portfolio_stats = self.val_env.get_portfolio_stats()
        
        # 计算综合评分
        eval_score = self.evaluator.calculate_score(portfolio_stats)
        
        # 记录评估结果
        self.writer.add_scalar('Evaluation/Score', eval_score, episode)
        self.writer.add_scalar('Evaluation/TotalReturn', 
                              portfolio_stats.get('total_return', 0), episode)
        self.writer.add_scalar('Evaluation/SharpeRatio', 
                              portfolio_stats.get('sharpe_ratio', 0), episode)
        self.writer.add_scalar('Evaluation/MaxDrawdown', 
                              portfolio_stats.get('max_drawdown', 0), episode)
        
        if final:
            self.logger.info(f"最终评估完成，评分: {eval_score:.4f}")
            self.logger.info(f"总收益率: {portfolio_stats.get('total_return', 0):.4f}")
            self.logger.info(f"夏普比率: {portfolio_stats.get('sharpe_ratio', 0):.4f}")
            self.logger.info(f"最大回撤: {portfolio_stats.get('max_drawdown', 0):.4f}")
        
        return eval_score
    
    def _should_early_stop(self, episode: int) -> bool:
        """检查是否应该早停"""
        if episode < EARLY_STOPPING_PATIENCE:
            return False
        
        # 检查最近的改进
        recent_scores = self.training_stats['evaluation_scores'][-EARLY_STOPPING_PATIENCE//EVAL_FREQUENCY:]
        
        if len(recent_scores) < 2:
            return False
        
        # 如果最近没有显著改进，则早停
        max_recent = max(recent_scores)
        improvement = max_recent - self.best_score
        
        return improvement < MIN_IMPROVEMENT
    
    def _save_best_model(self):
        """保存最佳模型"""
        model_path = get_model_path(self.run_id, 'best')
        self.multi_agent.save(model_path.replace('.pth', ''))
        
        # 保存特征融合模型
        fusion_path = model_path.replace('.pth', '_fusion.pth')
        torch.save({
            'model_state_dict': self.feature_fusion.state_dict(),
            'optimizer_state_dict': self.fusion_optimizer.state_dict(),
            'best_score': self.best_score,
            'best_episode': self.best_episode
        }, fusion_path)
    
    def _save_checkpoint(self, episode: int):
        """保存检查点"""
        checkpoint_path = get_model_path(self.run_id, f'checkpoint_{episode}')
        self.multi_agent.save(checkpoint_path.replace('.pth', ''))
        
        # 保存训练状态
        state_path = checkpoint_path.replace('.pth', '_state.pth')
        torch.save({
            'episode': episode,
            'training_stats': self.training_stats,
            'best_score': self.best_score,
            'best_episode': self.best_episode
        }, state_path)
    
    def _save_final_model(self):
        """保存最终模型"""
        final_path = get_model_path(self.run_id, 'final')
        self.multi_agent.save(final_path.replace('.pth', ''))
    
    def _generate_training_report(self, total_time: float, final_score: float) -> Dict:
        """生成训练报告"""
        report = {
            'run_id': self.run_id,
            'total_time': total_time,
            'total_episodes': len(self.training_stats['episode_rewards']),
            'final_score': final_score,
            'best_score': self.best_score,
            'best_episode': self.best_episode,
            'avg_episode_reward': np.mean(self.training_stats['episode_rewards']),
            'avg_episode_length': np.mean(self.training_stats['episode_lengths']),
            'final_portfolio_value': self.training_stats['portfolio_values'][-1] if self.training_stats['portfolio_values'] else 0
        }
        
        # 保存报告
        report_path = get_result_path(self.run_id)
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"训练报告已保存到: {report_path}")
        
        return report
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 奖励曲线
        axes[0, 0].plot(self.training_stats['episode_rewards'])
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Reward')
        
        # 投资组合价值
        axes[0, 1].plot(self.training_stats['portfolio_values'])
        axes[0, 1].set_title('Portfolio Value')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Value')
        
        # 损失曲线
        axes[1, 0].plot(self.training_stats['losses'])
        axes[1, 0].set_title('Training Loss')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Loss')
        
        # 评估分数
        if self.training_stats['evaluation_scores']:
            eval_episodes = list(range(EVAL_FREQUENCY-1, 
                                     len(self.training_stats['evaluation_scores']) * EVAL_FREQUENCY, 
                                     EVAL_FREQUENCY))
            axes[1, 1].plot(eval_episodes, self.training_stats['evaluation_scores'])
            axes[1, 1].set_title('Evaluation Scores')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Score')
        
        plt.tight_layout()
        
        # 保存图片
        plot_path = os.path.join(RESULT_DIR, f"{self.run_id}_training_curves.png")
        plt.savefig(plot_path, dpi=DPI)
        plt.close()
        
        self.logger.info(f"训练曲线已保存到: {plot_path}")

if __name__ == "__main__":
    # 测试代码
    print("测试训练器...")
    
    # 创建模拟数据
    np.random.seed(42)
    n_days = 200
    n_stocks = 2
    
    data = []
    for stock_id in range(n_stocks):
        for day in range(n_days):
            row = {
                'ts_code': f'stock_{stock_id}',
                'trade_date': f'2024-01-{day+1:02d}',
                'close': 10 + np.random.randn() * 0.5,
                'open': 10 + np.random.randn() * 0.5,
                'high': 10.5 + np.random.randn() * 0.3,
                'low': 9.5 + np.random.randn() * 0.3,
                'vol': 1000 + np.random.randn() * 100
            }
            
            # 添加技术指标
            for factor in TECHNICAL_FACTORS:
                if factor not in row:
                    row[factor] = np.random.randn()
            
            data.append(row)
    
    df = pd.DataFrame(data)
    
    # 分割训练和验证数据
    split_idx = int(len(df) * 0.8)
    train_df = df[:split_idx].copy()
    val_df = df[split_idx:].copy()
    
    # 创建训练器
    trainer = RLStockTrainer(train_df, val_df)
    
    # 简短训练测试
    report = trainer.train(n_episodes=5)
    
    print(f"训练完成，最终评分: {report['final_score']:.4f}")
    print("✓ 训练器测试通过")
