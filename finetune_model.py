# coding: utf-8
"""
股票预测模型微调脚本
用于使用新数据对已训练的模型进行增量学习和微调
"""

import os
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime
from sklearn.metrics import mean_squared_error, r2_score
import json
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
import config
from data_loader import DataProcessor
from models.samba_model import SAMBAModel
from utils import setup_seed


# 微调默认配置参数（仅作为默认值，所有参数通过命令行设置）
DEFAULT_FINETUNE_CONFIG = {
    'start_date': '2025-05-21',  # 微调数据开始日期
    'end_date': '2025-06-26',    # 微调数据结束日期
    'learning_rate': 0.0001,     # 微调学习率（比初始训练更小）
    'epochs': 10,                # 微调轮数
    'early_stop_patience': 10,   # 早停耐心
    'weight_decay': 1e-5,        # 权重衰减
    'freeze_layers': False,      # 是否冻结部分层
    'validation_split': 0.1,     # 验证集比例
    'batch_size': 128,            # 批处理大小
}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票预测模型微调')
    parser.add_argument('--model_path', type=str, required=True,
                       help='模型权重文件路径（支持原始训练模型或微调模型）')
    parser.add_argument('--start_date', type=str, default=DEFAULT_FINETUNE_CONFIG['start_date'],
                       help='微调数据开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, default=DEFAULT_FINETUNE_CONFIG['end_date'],
                       help='微调数据结束日期 (YYYY-MM-DD)')
    parser.add_argument('--lr', type=float, default=DEFAULT_FINETUNE_CONFIG['learning_rate'],
                       help='微调学习率')
    parser.add_argument('--epochs', type=int, default=DEFAULT_FINETUNE_CONFIG['epochs'],
                       help='微调轮数')
    parser.add_argument('--batch_size', type=int, default=DEFAULT_FINETUNE_CONFIG['batch_size'],
                       help='批处理大小')
    parser.add_argument('--early_stop_patience', type=int, default=DEFAULT_FINETUNE_CONFIG['early_stop_patience'],
                       help='早停耐心值')
    parser.add_argument('--weight_decay', type=float, default=DEFAULT_FINETUNE_CONFIG['weight_decay'],
                       help='权重衰减')
    parser.add_argument('--freeze_layers', action='store_true',
                       help='是否冻结部分层')
    parser.add_argument('--continue_from_checkpoint', type=str, default=None,
                       help='从指定检查点继续训练（可选）')
    parser.add_argument('--scheduler', type=str, default='plateau',
                       choices=['plateau', 'cosine', 'cosine_warm'],
                       help='学习率调度器类型: plateau(默认), cosine(余弦退火), cosine_warm(带热重启的余弦退火)')
    parser.add_argument('--min_lr', type=float, default=1e-7, help='最小学习率(用于余弦退火)')
    parser.add_argument('--t_max', type=int, default=None, help='余弦退火周期长度(默认为总epochs)')
    parser.add_argument('--t_0', type=int, default=5, help='热重启初始周期长度')
    parser.add_argument('--t_mult', type=int, default=2, help='热重启周期倍数')

    args = parser.parse_args()

    # 设置随机种子
    setup_seed(42)

    # 创建微调时间戳
    finetune_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"开始模型微调 - 时间戳: {finetune_timestamp}")
    print("="*60)

    # 验证日期范围
    if not validate_date_range(args.start_date, args.end_date):
        return

    # 智能解析模型路径和配置
    model_path = args.model_path
    model_dir = os.path.dirname(model_path)

    # 检查是否为微调模型
    is_finetune_model = '_finetune_' in model_dir

    if is_finetune_model:
        # 微调模型：查找微调配置文件
        config_path = os.path.join(model_dir, 'finetune_config.json')
        if not os.path.exists(config_path):
            print(f"错误：找不到微调配置文件 {config_path}")
            return
        print("检测到微调模型，将基于微调模型继续微调")
    else:
        # 原始训练模型：查找原始配置文件
        config_path = os.path.join(model_dir, 'config.json')
        if not os.path.exists(config_path):
            print(f"错误：找不到配置文件 {config_path}")
            return
        print("检测到原始训练模型，将进行首次微调")

    try:
        # 加载模型和配置
        print("1. 加载模型和配置...")
        model, run_config, original_checkpoint, model_type = load_existing_model(model_path, config_path)

        # 构建模型血缘信息
        if is_finetune_model:
            # 从微调配置中获取血缘信息
            with open(config_path, 'r', encoding='utf-8') as f:
                finetune_config_data = json.load(f)

            model_lineage = finetune_config_data.get('model_lineage', [])
            model_lineage.append({
                'timestamp': finetune_timestamp,
                'model_path': model_path,
                'model_type': model_type,
                'finetune_config': {
                    'start_date': args.start_date,
                    'end_date': args.end_date,
                    'learning_rate': args.lr,
                    'epochs': args.epochs,
                    'freeze_layers': args.freeze_layers
                }
            })

            # 使用原始配置
            original_config = finetune_config_data.get('original_config', run_config)
        else:
            # 首次微调，创建血缘信息
            model_lineage = [{
                'timestamp': 'original',
                'model_path': model_path,
                'model_type': model_type,
                'original_training': True
            }, {
                'timestamp': finetune_timestamp,
                'model_path': model_path,
                'model_type': model_type,
                'finetune_config': {
                    'start_date': args.start_date,
                    'end_date': args.end_date,
                    'learning_rate': args.lr,
                    'epochs': args.epochs,
                    'freeze_layers': args.freeze_layers
                }
            }]
            original_config = run_config

        print(f"模型训练信息：")
        print(f"  最佳验证损失: {original_checkpoint.get('best_val_loss', 'N/A')}")
        print(f"  最佳RIC: {original_checkpoint.get('best_ric', 'N/A')}")
        print(f"  模型类型: {model_type}")
        print(f"  微调历史: {len(model_lineage)-1} 次")

        # 准备微调数据
        print("\n2. 准备微调数据...")
        dataloaders, finetune_data_info = prepare_finetune_data(
            start_date=args.start_date,
            end_date=args.end_date,
            factors=original_config['factors'],
            target=original_config.get('target', config.TARGET),
            lookback_window=original_config.get('lookback_window', config.LOOKBACK_WINDOW),
            batch_size=args.batch_size
        )

        # 创建当前微调配置
        current_finetune_config = {
            'start_date': args.start_date,
            'end_date': args.end_date,
            'learning_rate': args.lr,
            'epochs': args.epochs,
            'batch_size': args.batch_size,
            'freeze_layers': args.freeze_layers,
            'model_source': model_path,
            'finetune_timestamp': finetune_timestamp
        }

        # 检查数据是否充足
        if len(dataloaders['train'].dataset) < 100:
            print(f"警告：训练数据量较少 ({len(dataloaders['train'].dataset)} 个样本)")
            print("建议增加日期范围或检查数据质量")

        # 验证特征一致性
        expected_feature_count = len(original_config.get('factors', []))
        actual_feature_count = len(finetune_data_info['feature_names'])
        if actual_feature_count != expected_feature_count:
            print(f"警告：微调数据的特征数量({actual_feature_count})与原始模型({expected_feature_count})不一致")

        print(f"微调数据特征数: {actual_feature_count}")
        print(f"微调数据日期范围: {finetune_data_info['train_dates'][0]} 至 {finetune_data_info['train_dates'][-1]}")

        # 检查日期范围覆盖情况
        if len(finetune_data_info['train_dates']) > 0 and len(finetune_data_info['val_dates']) > 0:
            # 获取完整的日期范围
            all_dates = np.concatenate([finetune_data_info['train_dates'], finetune_data_info['val_dates']])
            actual_start = pd.to_datetime(all_dates[0]).strftime('%Y-%m-%d')
            actual_end = pd.to_datetime(all_dates[-1]).strftime('%Y-%m-%d')

            user_start = pd.to_datetime(args.start_date)
            user_end = pd.to_datetime(args.end_date)
            actual_start_dt = pd.to_datetime(actual_start)
            actual_end_dt = pd.to_datetime(actual_end)

            # 检查覆盖情况
            start_covered = actual_start_dt <= user_start
            end_covered = actual_end_dt >= user_end

            if start_covered and end_covered:
                print("✓ 实际数据范围完全覆盖用户设置的日期范围")
            elif start_covered:
                print(f"⚠ 覆盖开始日期，但结束日期受交易日历影响 (最后交易日: {actual_end})")
            elif end_covered:
                print(f"⚠ 覆盖结束日期，但开始日期受交易日历影响 (首个交易日: {actual_start})")
            else:
                print(f"⚠ 实际范围({actual_start} 至 {actual_end})受交易日历影响，与设置范围({args.start_date} 至 {args.end_date})有差异")
        else:
            print("⚠ 没有有效的微调数据")

        print(f"说明: 时序模型为每个交易日使用前{original_config.get('lookback_window', config.LOOKBACK_WINDOW)}个交易日的数据作为特征")

        # 设置微调优化器
        print("\n3. 设置微调优化器...")
        optimizer = setup_finetune_optimizer(
            model=model,
            learning_rate=args.lr,
            weight_decay=args.weight_decay,
            freeze_layers=args.freeze_layers
        )

        # 创建学习率调度器
        scheduler, need_step_per_epoch = create_finetune_scheduler(optimizer, args.scheduler, args)
        print(f"使用学习率调度器: {args.scheduler}")
        if args.scheduler == 'cosine':
            t_max = args.t_max if args.t_max else args.epochs
            print(f"余弦退火参数: T_max={t_max}, eta_min={args.min_lr}")
        elif args.scheduler == 'cosine_warm':
            print(f"热重启余弦退火参数: T_0={args.t_0}, T_mult={args.t_mult}, eta_min={args.min_lr}")

        # 定义损失函数
        criterion = nn.MSELoss()

        # 初始化微调变量
        best_val_loss = float('inf')
        best_ric = float('-inf')
        best_ic = float('-inf')  # 新增：最佳IC跟踪
        train_losses = []
        val_losses = []
        val_metrics_history = []
        early_stop_counter = 0

        print(f"\n4. 开始微调，共 {args.epochs} 轮...")
        print("="*60)

        # 微调循环
        for epoch in range(args.epochs):
            print(f"\n微调轮次 {epoch+1}/{args.epochs}:")

            # 微调一个epoch
            train_loss = finetune_epoch(
                model, dataloaders['train'], criterion, optimizer, config.DEVICE
            )
            train_losses.append(train_loss)

            # 在验证集上评估
            val_loss, val_metrics = validate_model(
                model, dataloaders['val'], criterion, config.DEVICE
            )

            # 根据调度器类型调用step()
            if need_step_per_epoch:
                scheduler.step()  # 余弦退火调度器每个epoch调用
            else:
                scheduler.step(val_loss)  # ReduceLROnPlateau需要传入metric

            val_losses.append(val_loss)
            val_metrics_history.append(val_metrics)

            # 打印当前学习率
            current_lr = optimizer.param_groups[0]['lr']
            print(f"当前学习率: {current_lr:.8f}")

            # 打印当前结果
            print(f"训练损失: {train_loss:.6f}, 验证损失: {val_loss:.6f}")
            print(f"验证指标: RMSE={val_metrics['rmse']:.6f}, R²={val_metrics['r2']:.6f}, "
                  f"IC={val_metrics['ic']:.6f}, RIC={val_metrics['ric']:.6f}")

            # 检查是否为最佳模型
            is_best = val_loss < best_val_loss
            if is_best:
                best_val_loss = val_loss
                early_stop_counter = 0
                print("发现新的最佳损失微调模型!")
            else:
                early_stop_counter += 1
                print(f"早停计数: {early_stop_counter}/{args.early_stop_patience}")

            # 检查是否为最佳RIC模型
            is_best_ric = val_metrics['ric'] > best_ric
            if is_best_ric:
                best_ric = val_metrics['ric']
                print("发现新的最佳RIC微调模型!")

            # 检查是否为最佳IC模型
            is_best_ic = val_metrics['ic'] > best_ic
            if is_best_ic:
                best_ic = val_metrics['ic']
                print("发现新的最佳IC微调模型!")

            # 保存微调模型（只保存4个检查点：最佳IC、最佳RIC、最佳损失、最后一轮）
            save_finetuned_model(
                model, optimizer, epoch, val_metrics,
                best_val_loss, best_ric, best_ic, is_best, is_best_ric, is_best_ic,
                original_config, current_finetune_config,
                finetune_timestamp, model_lineage, args.epochs
            )

            # 早停检查
            if early_stop_counter >= args.early_stop_patience:
                print(f"\n早停触发！在第 {epoch+1} 轮停止微调")
                # 保存早停时的最终模型
                final_checkpoint = {
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_metrics': val_metrics,
                    'best_val_loss': best_val_loss,
                    'best_ric': best_ric,
                    'best_ic': best_ic,
                    'finetune_timestamp': finetune_timestamp,
                    'model_lineage': model_lineage,
                    'training_completed': True,
                    'early_stopped': True,
                    'early_stop_epoch': epoch
                }
                run_id = original_config.get('run_id', 'unknown')
                finetune_dir = os.path.join(config.OUTPUT_DIR, f"{run_id}_finetune_{finetune_timestamp}")
                final_path = os.path.join(finetune_dir, 'final_finetune_model.pt')
                torch.save(final_checkpoint, final_path)
                print(f"保存早停最终微调模型: {final_path}")
                break

        print("\n5. 微调完成！")
        print("="*60)
        print(f"最佳验证损失: {best_val_loss:.6f}")
        print(f"最佳RIC值: {best_ric:.6f}")
        print(f"最佳IC值: {best_ic:.6f}")

        # 保存微调历史
        run_id = original_config.get('run_id', 'unknown')
        finetune_dir = os.path.join(config.OUTPUT_DIR, f"{run_id}_finetune_{finetune_timestamp}")

        history = {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_metrics_history': val_metrics_history,
            'model_lineage': model_lineage,
            'original_config': original_config,
            'finetune_config': current_finetune_config,
            'training_summary': {
                'total_epochs': len(train_losses),
                'best_val_loss': best_val_loss,
                'best_ric': best_ric,
                'best_ic': best_ic,
                'final_val_loss': val_losses[-1] if val_losses else None,
                'final_ric': val_metrics_history[-1]['ric'] if val_metrics_history else None,
                'final_ic': val_metrics_history[-1]['ic'] if val_metrics_history else None,
                'training_completed': True
            }
        }

        history_path = os.path.join(finetune_dir, 'finetune_history.json')
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2, ensure_ascii=False, default=str)

        print(f"微调历史已保存: {history_path}")
        print(f"微调模型保存目录: {finetune_dir}")

    except Exception as e:
        print(f"微调过程中发生错误: {e}")
        import traceback
        traceback.print_exc()






def validate_date_range(start_date, end_date):
    """
    验证日期范围的有效性
    
    参数:
        start_date: 开始日期字符串 (YYYY-MM-DD)
        end_date: 结束日期字符串 (YYYY-MM-DD)
        
    返回:
        bool: 日期范围是否有效
    """
    try:
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        if start_dt >= end_dt:
            print(f"错误：开始日期 {start_date} 必须早于结束日期 {end_date}")
            return False
            
        # 检查日期范围是否合理（至少30天）
        if (end_dt - start_dt).days < 30:
            print(f"警告：日期范围过短，建议至少30天以上")
            
        print(f"日期范围验证通过：{start_date} 至 {end_date}")
        return True
        
    except Exception as e:
        print(f"日期格式错误：{e}")
        return False

def load_existing_model(model_path, run_config_path):
    """
    加载已训练的模型和配置

    参数:
        model_path: 模型权重文件路径
        run_config_path: 运行配置文件路径

    返回:
        model: 加载的模型
        run_config: 运行配置
        checkpoint: 检查点信息
        model_type: 模型类型（从文件名推断）
    """
    print(f"正在加载模型：{model_path}")

    # 检查文件是否存在
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"找不到模型文件：{model_path}")
    if not os.path.exists(run_config_path):
        raise FileNotFoundError(f"找不到配置文件：{run_config_path}")

    # 从文件名推断模型类型
    model_filename = os.path.basename(model_path)
    if 'best_ric' in model_filename:
        model_type = 'best_ric'
    elif 'best_model' in model_filename or 'best_finetune' in model_filename:
        model_type = 'best_loss'
    elif 'finetune' in model_filename:
        model_type = 'finetuned'
    else:
        model_type = 'unknown'

    print(f"检测到模型类型：{model_type}")

    # 加载运行配置
    with open(run_config_path, 'r', encoding='utf-8') as f:
        run_config = json.load(f)

    print("模型配置信息：")
    for key, value in run_config.items():
        if key != 'factors':  # factors列表太长，不打印
            print(f"  {key}: {value}")
    print(f"  特征数量: {len(run_config['factors'])}")

    # 初始化模型
    model = SAMBAModel(
        input_dim=len(run_config['factors']),
        d_model=run_config['d_model'],
        n_layer=run_config['n_layer'],
        num_heads=run_config['num_heads'],
        gnn_k=run_config['gnn_k'],
        node_embedding_dim=run_config['node_embedding_dim'],
        cnn_blocks=run_config['cnn_blocks'],
        cnn_kernel_sizes=run_config['cnn_kernel_sizes'],
        cnn_bottleneck_scale=run_config['cnn_bottleneck_scale'],
        dropout=run_config['dropout']
    ).to(config.DEVICE)

    # 加载模型权重
    checkpoint = torch.load(model_path, map_location=config.DEVICE, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])

    print(f"模型加载成功，共 {sum(p.numel() for p in model.parameters())} 个参数")

    return model, run_config, checkpoint, model_type

def prepare_finetune_data(start_date, end_date, factors, target, lookback_window, batch_size):
    """
    准备微调数据

    参数:
        start_date: 开始日期
        end_date: 结束日期
        factors: 因子列表
        target: 目标变量
        lookback_window: 时间窗口
        batch_size: 批处理大小

    返回:
        dataloaders: 数据加载器字典
        data_info: 数据信息
    """
    print("正在准备微调数据...")

    # 检查数据文件是否存在
    if not os.path.exists(config.DATA_PATH):
        raise FileNotFoundError(f"找不到数据文件：{config.DATA_PATH}")

    # 智能扩展开始日期以包含足够的历史数据
    import pandas as pd
    start_date_dt = pd.to_datetime(start_date)

    # 智能扩展开始日期：确保能够为用户指定范围内的所有交易日创建序列
    # 考虑到交易日历的复杂性，我们需要足够的缓冲
    calendar_days_to_extend = lookback_window * 2  # 2倍缓冲，考虑周末和节假日
    extended_start_date = start_date_dt - pd.Timedelta(days=calendar_days_to_extend)
    extended_start_date_str = extended_start_date.strftime('%Y-%m-%d')

    print(f"用户设置日期范围: {start_date} 至 {end_date}")
    print(f"数据加载日期范围: {extended_start_date_str} 至 {end_date}")
    print(f"扩展说明: 向前扩展{calendar_days_to_extend}天以确保用户范围内的交易日都有{lookback_window}天历史数据")

    # 创建数据处理器，使用扩展的开始日期
    data_processor = DataProcessor(
        data_path=config.DATA_PATH,
        factors=factors,
        target=target,
        lookback=lookback_window,
        batch_size=batch_size,
        start_date=extended_start_date_str,  # 使用扩展的开始日期
        end_date=end_date
    )

    # 加载和处理数据
    dataloaders, data_info = data_processor.prepare_data()

    # 过滤数据，只保留用户指定日期范围内的样本
    filtered_dataloaders, filtered_data_info = filter_data_by_date_range(
        dataloaders, data_info, start_date, end_date
    )

    print(f"微调数据准备完成：")
    print(f"  训练集：{len(filtered_dataloaders['train'].dataset)} 个样本")
    print(f"  验证集：{len(filtered_dataloaders['val'].dataset)} 个样本")
    print(f"  特征数量：{len(filtered_data_info['feature_names'])}")

    # 显示完整的日期范围（训练集+验证集）
    all_dates = np.concatenate([filtered_data_info['train_dates'], filtered_data_info['val_dates']])
    if len(all_dates) > 0:
        print(f"  实际数据日期范围：{all_dates[0]} 至 {all_dates[-1]}")
    else:
        print(f"  实际数据日期范围：无有效数据")

    return filtered_dataloaders, filtered_data_info

def filter_data_by_date_range(dataloaders, data_info, start_date, end_date):
    """
    根据指定日期范围过滤数据，并重新划分为训练集和验证集（微调不需要测试集）

    参数:
        dataloaders: 原始数据加载器
        data_info: 原始数据信息
        start_date: 目标开始日期
        end_date: 目标结束日期

    返回:
        filtered_dataloaders: 过滤后的数据加载器
        filtered_data_info: 过滤后的数据信息
    """
    import pandas as pd
    from torch.utils.data import DataLoader
    from data_loader import StockDataset

    start_date_dt = pd.to_datetime(start_date)
    end_date_dt = pd.to_datetime(end_date)

    # 合并所有数据（训练+验证+测试）
    all_features = []
    all_targets = []
    all_dates = []
    all_codes = []

    for split in ['train', 'val', 'test']:
        if f'{split}_dates' in data_info:
            dates = data_info[f'{split}_dates']
            codes = data_info[f'{split}_codes']

            # 获取数据集中的特征和目标
            dataset = dataloaders[split].dataset
            for i in range(len(dataset)):
                features, targets = dataset[i]
                all_features.append(features.numpy())
                all_targets.append(targets.numpy())
                all_dates.append(dates[i])
                all_codes.append(codes[i])

    # 转换为numpy数组
    all_features = np.array(all_features)
    all_targets = np.array(all_targets)
    all_dates = np.array(all_dates)

    # 按日期过滤
    mask = (pd.to_datetime(all_dates) >= start_date_dt) & (pd.to_datetime(all_dates) <= end_date_dt)
    filtered_features = all_features[mask]
    filtered_targets = all_targets[mask]
    filtered_dates = all_dates[mask]
    filtered_codes = [all_codes[i] for i in range(len(all_codes)) if mask[i]]

    if len(filtered_features) == 0:
        print("警告：过滤后没有有效数据")
        # 创建空的数据加载器
        empty_dataset = StockDataset([], [])
        filtered_dataloaders = {
            'train': DataLoader(empty_dataset, batch_size=1),
            'val': DataLoader(empty_dataset, batch_size=1)
        }
        filtered_data_info = {
            'train_dates': [],
            'val_dates': [],
            'train_codes': [],
            'val_codes': [],
            'feature_names': data_info['feature_names'],
            'target_name': data_info['target_name'],
            'scaler': data_info['scaler']
        }
        return filtered_dataloaders, filtered_data_info

    # 重新划分为训练集和验证集（90%训练，10%验证，不要测试集）
    train_size = int(len(filtered_features) * 0.9)

    train_features = filtered_features[:train_size]
    train_targets = filtered_targets[:train_size]
    train_dates = filtered_dates[:train_size]
    train_codes = filtered_codes[:train_size]

    val_features = filtered_features[train_size:]
    val_targets = filtered_targets[train_size:]
    val_dates = filtered_dates[train_size:]
    val_codes = filtered_codes[train_size:]

    # 创建数据集
    train_dataset = StockDataset(train_features, train_targets)
    val_dataset = StockDataset(val_features, val_targets)

    # 创建数据加载器
    batch_size = dataloaders['train'].batch_size
    num_workers = dataloaders['train'].num_workers
    pin_memory = dataloaders['train'].pin_memory

    filtered_dataloaders = {
        'train': DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=pin_memory
        ),
        'val': DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=pin_memory
        )
    }

    filtered_data_info = {
        'train_dates': train_dates,
        'val_dates': val_dates,
        'train_codes': train_codes,
        'val_codes': val_codes,
        'feature_names': data_info['feature_names'],
        'target_name': data_info['target_name'],
        'scaler': data_info['scaler']
    }

    return filtered_dataloaders, filtered_data_info

def setup_finetune_optimizer(model, learning_rate, weight_decay, freeze_layers=False):
    """
    设置微调优化器
    
    参数:
        model: 模型
        learning_rate: 学习率
        weight_decay: 权重衰减
        freeze_layers: 是否冻结部分层
        
    返回:
        optimizer: 优化器
    """
    if freeze_layers:
        # 冻结前几层，只微调后面的层
        print("冻结模型前半部分层，只微调后半部分...")
        
        # 获取所有参数
        all_params = list(model.named_parameters())
        total_layers = len(all_params)
        freeze_count = total_layers // 2
        
        # 冻结前半部分参数
        for i, (name, param) in enumerate(all_params):
            if i < freeze_count:
                param.requires_grad = False
                print(f"  冻结层：{name}")
            else:
                param.requires_grad = True
        
        # 只优化未冻结的参数
        trainable_params = [p for p in model.parameters() if p.requires_grad]
        print(f"可训练参数数量：{sum(p.numel() for p in trainable_params)}")
        
    else:
        print("微调所有模型参数...")
        trainable_params = model.parameters()
    
    # 使用较小的学习率进行微调
    optimizer = optim.AdamW(
        trainable_params,
        lr=learning_rate,
        weight_decay=weight_decay
    )
    
    return optimizer


def create_finetune_scheduler(optimizer, scheduler_type, args):
    """
    创建微调学习率调度器

    参数:
        optimizer: 优化器
        scheduler_type: 调度器类型 ('plateau', 'cosine', 'cosine_warm')
        args: 命令行参数

    返回:
        scheduler: 学习率调度器
        need_step_per_epoch: 是否需要每个epoch调用step()
    """
    if scheduler_type == 'plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=5,
            verbose=True
        )
        return scheduler, False  # ReduceLROnPlateau需要传入metric

    elif scheduler_type == 'cosine':
        # 余弦退火调度器
        t_max = args.t_max if args.t_max else args.epochs
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=t_max,
            eta_min=args.min_lr,
            verbose=True
        )
        return scheduler, True  # 每个epoch调用step()

    elif scheduler_type == 'cosine_warm':
        # 带热重启的余弦退火调度器
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=args.t_0,
            T_mult=args.t_mult,
            eta_min=args.min_lr,
            verbose=True
        )
        return scheduler, True  # 每个epoch调用step()

    else:
        raise ValueError(f"不支持的调度器类型: {scheduler_type}")


def finetune_epoch(model, train_loader, criterion, optimizer, device):
    """
    微调一个epoch
    
    参数:
        model: 模型
        train_loader: 训练数据加载器
        criterion: 损失函数
        optimizer: 优化器
        device: 设备
        
    返回:
        avg_loss: 平均损失
    """
    model.train()
    total_loss = 0
    
    for features, targets in tqdm(train_loader, desc="微调中"):
        features, targets = features.to(device), targets.to(device)
        
        # 前向传播
        outputs = model(features)
        loss = criterion(outputs, targets)
        
        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪（微调时使用更小的梯度裁剪）
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
        
        optimizer.step()
        
        total_loss += loss.item() * features.size(0)
    
    avg_loss = total_loss / len(train_loader.dataset)
    return avg_loss

def validate_model(model, val_loader, criterion, device):
    """
    验证模型性能
    
    参数:
        model: 模型
        val_loader: 验证数据加载器
        criterion: 损失函数
        device: 设备
        
    返回:
        val_loss: 验证损失
        val_metrics: 验证指标字典
    """
    model.eval()
    total_loss = 0
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for features, targets in val_loader:
            features, targets = features.to(device), targets.to(device)
            
            outputs = model(features)
            loss = criterion(outputs, targets)
            
            total_loss += loss.item() * features.size(0)
            
            all_predictions.extend(outputs.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
    
    val_loss = total_loss / len(val_loader.dataset)
    
    # 计算评估指标
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    rmse = np.sqrt(mean_squared_error(all_targets, all_predictions))
    r2 = r2_score(all_targets, all_predictions)
    
    # 计算IC和RIC
    ic = np.corrcoef(all_targets, all_predictions)[0, 1] if len(all_targets) > 1 else 0
    
    # 计算RIC（排序相关系数）
    from scipy.stats import spearmanr
    ric, _ = spearmanr(all_targets, all_predictions)
    ric = ric if not np.isnan(ric) else 0
    
    val_metrics = {
        'rmse': rmse,
        'r2': r2,
        'ic': ic,
        'ric': ric
    }
    
    return val_loss, val_metrics

def save_finetuned_model(model, optimizer, epoch, val_metrics, best_val_loss, best_ric, best_ic,
                        is_best, is_best_ric, is_best_ic, original_config, finetune_config,
                        finetune_timestamp, model_lineage, total_epochs):
    """
    保存微调后的模型（只保存4个检查点：最佳IC、最佳RIC、最佳损失、最后一轮）

    参数:
        model: 模型
        optimizer: 优化器
        epoch: 当前轮数
        val_metrics: 验证指标
        best_val_loss: 最佳验证损失
        best_ric: 最佳RIC值
        best_ic: 最佳IC值
        is_best: 是否为最佳损失模型
        is_best_ric: 是否为最佳RIC模型
        is_best_ic: 是否为最佳IC模型
        original_config: 原始模型配置
        finetune_config: 微调配置
        finetune_timestamp: 微调时间戳
        model_lineage: 模型血缘信息
        total_epochs: 总轮数
    """
    # 创建微调模型保存目录
    run_id = original_config.get('run_id', 'unknown')
    finetune_dir = os.path.join(config.OUTPUT_DIR, f"{run_id}_finetune_{finetune_timestamp}")
    os.makedirs(finetune_dir, exist_ok=True)

    # 准备完整的检查点数据
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'val_metrics': val_metrics,
        'best_val_loss': best_val_loss,
        'best_ric': best_ric,
        'best_ic': best_ic,
        'finetune_timestamp': finetune_timestamp,
        'model_lineage': model_lineage,  # 模型血缘信息
        'training_completed': False  # 标记训练是否完成
    }

    # 只保存4个检查点：最佳IC、最佳RIC、最佳损失、最后一轮
    # 1. 保存最佳损失模型
    if is_best:
        best_checkpoint = checkpoint.copy()
        best_checkpoint['is_best_loss'] = True
        best_path = os.path.join(finetune_dir, 'best_loss_finetune_model.pt')
        torch.save(best_checkpoint, best_path)
        print(f"保存最佳损失微调模型: {best_path}")

    # 2. 保存最佳RIC模型
    if is_best_ric:
        best_ric_checkpoint = checkpoint.copy()
        best_ric_checkpoint['is_best_ric'] = True
        best_ric_path = os.path.join(finetune_dir, 'best_ric_finetune_model.pt')
        torch.save(best_ric_checkpoint, best_ric_path)
        print(f"保存最佳RIC微调模型: {best_ric_path}")

    # 3. 保存最佳IC模型
    if is_best_ic:
        best_ic_checkpoint = checkpoint.copy()
        best_ic_checkpoint['is_best_ic'] = True
        best_ic_path = os.path.join(finetune_dir, 'best_ic_finetune_model.pt')
        torch.save(best_ic_checkpoint, best_ic_path)
        print(f"保存最佳IC微调模型: {best_ic_path}")

    # 4. 保存最后一轮模型（如果是最后一轮或早停）
    is_final_epoch = (epoch == total_epochs - 1)
    if is_final_epoch:
        final_checkpoint = checkpoint.copy()
        final_checkpoint['is_final_epoch'] = True
        final_checkpoint['training_completed'] = True
        final_path = os.path.join(finetune_dir, 'final_finetune_model.pt')
        torch.save(final_checkpoint, final_path)
        print(f"保存最终微调模型: {final_path}")

    # 保存完整的配置信息（支持继续微调）
    complete_config = {
        # 原始模型配置
        'original_config': original_config,
        # 微调配置
        'finetune_config': finetune_config,
        # 模型血缘信息
        'model_lineage': model_lineage,
        # 当前状态
        'current_epoch': epoch,
        'best_val_loss': best_val_loss,
        'best_ric': best_ric,
        'best_ic': best_ic,
        'finetune_timestamp': finetune_timestamp,

        # 用于继续微调的信息
        'can_continue_finetune': True,
        'available_checkpoints': {
            'best_loss': 'best_loss_finetune_model.pt' if is_best else None,
            'best_ric': 'best_ric_finetune_model.pt' if is_best_ric else None,
            'best_ic': 'best_ic_finetune_model.pt' if is_best_ic else None,
            'final': 'final_finetune_model.pt' if is_final_epoch else None
        }
    }

    config_path = os.path.join(finetune_dir, 'finetune_config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(complete_config, f, indent=2, ensure_ascii=False, default=str)

    print(f"保存完整配置信息: {config_path}")

if __name__ == "__main__":
    main()
