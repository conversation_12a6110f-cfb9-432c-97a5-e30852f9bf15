#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
深度强化学习股票预测系统训练脚本
整合DQN多智能体、动态分块和多模态特征融合
"""

import argparse
import pandas as pd
import numpy as np
import torch
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import *
from training.trainer import RLStockTrainer
from data.data_loader import StockDataLoader
from utils.metrics import setup_logging

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='深度强化学习股票预测系统训练')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, 
                       default=STOCK_DATA_PATH,
                       help='股票数据文件路径')
    parser.add_argument('--train_start_date', type=str, 
                       default='2020-01-01',
                       help='训练开始日期')
    parser.add_argument('--train_end_date', type=str, 
                       default='2023-12-31',
                       help='训练结束日期')
    parser.add_argument('--val_start_date', type=str, 
                       default='2024-01-01',
                       help='验证开始日期')
    parser.add_argument('--val_end_date', type=str, 
                       default='2024-12-31',
                       help='验证结束日期')
    
    # 训练参数
    parser.add_argument('--n_episodes', type=int, 
                       default=N_EPISODES,
                       help='训练轮数')
    parser.add_argument('--n_agents', type=int, 
                       default=N_AGENTS,
                       help='智能体数量')
    parser.add_argument('--coordination_method', type=str, 
                       default='attention',
                       choices=['attention', 'voting', 'weighted'],
                       help='智能体协调方法')
    parser.add_argument('--batch_size', type=int, 
                       default=BATCH_SIZE,
                       help='批次大小')
    parser.add_argument('--learning_rate', type=float, 
                       default=LEARNING_RATE,
                       help='学习率')
    
    # 环境参数
    parser.add_argument('--initial_capital', type=float, 
                       default=INITIAL_CAPITAL,
                       help='初始资金')
    parser.add_argument('--transaction_cost', type=float, 
                       default=TRANSACTION_COST,
                       help='交易成本')
    parser.add_argument('--max_position_size', type=float, 
                       default=MAX_POSITION_SIZE,
                       help='最大仓位')
    
    # 模型参数
    parser.add_argument('--lookback_window', type=int, 
                       default=LOOKBACK_WINDOW,
                       help='历史数据窗口')
    parser.add_argument('--prediction_horizon', type=int, 
                       default=PREDICTION_HORIZON,
                       help='预测时间范围')
    
    # 输出参数
    parser.add_argument('--run_id', type=str, 
                       default=None,
                       help='运行ID')
    parser.add_argument('--save_dir', type=str, 
                       default=MODEL_DIR,
                       help='模型保存目录')
    parser.add_argument('--log_level', type=str, 
                       default=LOG_LEVEL,
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    # 其他参数
    parser.add_argument('--seed', type=int, 
                       default=SEED,
                       help='随机种子')
    parser.add_argument('--device', type=str, 
                       default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='计算设备')
    parser.add_argument('--resume_from', type=str, 
                       default=None,
                       help='从检查点恢复训练')
    
    return parser.parse_args()

def setup_environment(args):
    """设置训练环境"""
    # 设置随机种子
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(args.seed)
        torch.cuda.manual_seed_all(args.seed)
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    # 更新全局配置
    global DEVICE
    DEVICE = device
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 创建目录
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)
    os.makedirs(RESULT_DIR, exist_ok=True)
    
    logging.info(f"训练环境设置完成")
    logging.info(f"设备: {device}")
    logging.info(f"随机种子: {args.seed}")

def load_data(args):
    """加载和预处理数据"""
    logging.info("开始加载数据...")
    
    try:
        # 检查数据文件是否存在
        if not os.path.exists(args.data_path):
            # 如果指定路径不存在，尝试使用相对路径
            relative_path = os.path.join('..', args.data_path)
            if os.path.exists(relative_path):
                args.data_path = relative_path
            else:
                raise FileNotFoundError(f"数据文件不存在: {args.data_path}")
        
        # 加载数据
        data_loader = StockDataLoader()
        full_data = data_loader.load_data(args.data_path)
        
        logging.info(f"数据加载完成，共 {len(full_data)} 条记录")
        
        # 数据预处理
        processed_data = data_loader.preprocess_data(
            full_data, 
            features=TECHNICAL_FACTORS,
            target=TARGET_VARIABLE
        )
        
        # 分割训练和验证数据
        train_data = data_loader.filter_by_date(
            processed_data, 
            args.train_start_date, 
            args.train_end_date
        )
        
        val_data = data_loader.filter_by_date(
            processed_data, 
            args.val_start_date, 
            args.val_end_date
        )
        
        logging.info(f"训练数据: {len(train_data)} 条记录")
        logging.info(f"验证数据: {len(val_data)} 条记录")
        
        if len(train_data) == 0:
            raise ValueError("训练数据为空，请检查日期范围和数据文件")
        
        if len(val_data) == 0:
            logging.warning("验证数据为空，将使用训练数据的一部分作为验证数据")
            # 使用训练数据的最后20%作为验证数据
            split_idx = int(len(train_data) * 0.8)
            val_data = train_data[split_idx:].copy()
            train_data = train_data[:split_idx].copy()
        
        return train_data, val_data
        
    except Exception as e:
        logging.error(f"数据加载失败: {str(e)}")
        # 创建模拟数据用于测试
        logging.info("创建模拟数据用于测试...")
        train_data, val_data = create_mock_data()
        return train_data, val_data

def create_mock_data():
    """创建模拟数据用于测试"""
    np.random.seed(42)
    
    n_days = 500
    n_stocks = 3
    
    data = []
    base_date = pd.to_datetime('2020-01-01')
    
    for stock_id in range(n_stocks):
        price = 10.0  # 初始价格
        
        for day in range(n_days):
            # 模拟价格随机游走
            price_change = np.random.normal(0, 0.02)
            price = max(price * (1 + price_change), 1.0)  # 确保价格为正
            
            current_date = base_date + pd.Timedelta(days=day)
            
            row = {
                'ts_code': f'stock_{stock_id:03d}',
                'trade_date': current_date.strftime('%Y-%m-%d'),
                'close': price,
                'open': price * (1 + np.random.normal(0, 0.01)),
                'high': price * (1 + abs(np.random.normal(0, 0.015))),
                'low': price * (1 - abs(np.random.normal(0, 0.015))),
                'vol': np.random.lognormal(10, 1),
                'amount': price * np.random.lognormal(10, 1),
                'pct_chg': price_change * 100
            }
            
            # 添加技术指标（模拟）
            for factor in TECHNICAL_FACTORS:
                if factor not in row:
                    row[factor] = np.random.normal(0, 1)
            
            # 添加目标变量
            row[TARGET_VARIABLE] = np.random.normal(0, 5)
            
            data.append(row)
    
    df = pd.DataFrame(data)
    
    # 分割训练和验证数据
    split_date = '2023-01-01'
    train_data = df[df['trade_date'] < split_date].copy()
    val_data = df[df['trade_date'] >= split_date].copy()
    
    logging.info(f"创建模拟数据完成 - 训练: {len(train_data)}, 验证: {len(val_data)}")
    
    return train_data, val_data

def main():
    """主函数"""
    # 解析参数
    args = parse_arguments()
    
    # 生成运行ID
    if args.run_id is None:
        args.run_id = f"rl_stock_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print(f"开始训练深度强化学习股票预测系统")
    print(f"运行ID: {args.run_id}")
    print("=" * 60)
    
    try:
        # 设置环境
        setup_environment(args)
        
        # 加载数据
        train_data, val_data = load_data(args)
        
        # 创建训练器
        trainer = RLStockTrainer(
            train_data=train_data,
            val_data=val_data,
            run_id=args.run_id
        )
        
        # 开始训练
        logging.info("开始训练...")
        training_report = trainer.train(n_episodes=args.n_episodes)
        
        # 绘制训练曲线
        trainer.plot_training_curves()
        
        # 打印训练结果
        print("\n" + "=" * 60)
        print("训练完成！")
        print("=" * 60)
        print(f"运行ID: {training_report['run_id']}")
        print(f"训练时间: {training_report['total_time']:.2f} 秒")
        print(f"总轮数: {training_report['total_episodes']}")
        print(f"最终评分: {training_report['final_score']:.4f}")
        print(f"最佳评分: {training_report['best_score']:.4f} (第 {training_report['best_episode']} 轮)")
        print(f"平均轮次奖励: {training_report['avg_episode_reward']:.4f}")
        print(f"最终投资组合价值: {training_report['final_portfolio_value']:.2f}")
        
        # 保存配置
        config_path = os.path.join(RESULT_DIR, f"{args.run_id}_config.txt")
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write("训练配置:\n")
            for key, value in vars(args).items():
                f.write(f"{key}: {value}\n")
        
        logging.info(f"训练配置已保存到: {config_path}")
        logging.info("训练完成！")
        
        return training_report
        
    except Exception as e:
        logging.error(f"训练过程中发生错误: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        raise

if __name__ == "__main__":
    main()
