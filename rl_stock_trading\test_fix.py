#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的维度问题
"""

import torch
import numpy as np
import sys
import os
sys.path.append('.')

from utils.config import *
from models.multimodal.feature_fusion import MultiModalFeatureFusion

def test_multimodal_fusion():
    """测试多模态特征融合"""
    print("测试多模态特征融合...")
    
    batch_size = 2
    seq_len = LOOKBACK_WINDOW
    input_dim = len(TECHNICAL_FACTORS)
    output_dim = 256
    
    # 创建测试数据
    x = torch.randn(batch_size, seq_len, input_dim)
    print(f"输入形状: {x.shape}")
    print(f"技术因子数量: {len(TECHNICAL_FACTORS)}")
    print(f"回看窗口: {LOOKBACK_WINDOW}")
    
    # 创建模型
    model = MultiModalFeatureFusion(input_dim, output_dim)
    
    try:
        # 前向传播
        fused_features, aux_outputs = model(x)
        print(f"融合特征形状: {fused_features.shape}")
        print(f"门控权重形状: {aux_outputs['gate_weights'].shape}")
        print("✓ 多模态特征融合测试通过")
        return True
    except Exception as e:
        print(f"✗ 多模态特征融合测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_multimodal_fusion()
    print(f"测试结果: {'成功' if success else '失败'}")
