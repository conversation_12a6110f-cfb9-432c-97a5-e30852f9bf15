# 深度强化学习股票预测系统

基于两篇前沿论文实现的深度强化学习股票预测和交易系统：
1. **Deep Q-Network (DQN) multi-agent reinforcement learning (MARL) for Stock Trading** (2505.03949v1)
2. **Dynamic Chunking for End-to-End Hierarchical Sequence Modeling** (2507.07955v1)

## 系统架构

### 核心组件

1. **DQN多智能体系统** (`agents/`)
   - 多个DQN智能体协同决策
   - 支持买入、卖出、持有三种动作
   - 经验回放和目标网络更新
   - 智能体间信息共享机制

2. **动态分块序列建模** (`models/dynamic_chunking/`)
   - 分层序列处理架构
   - 自适应分块策略
   - 端到端可训练的分块机制
   - 长序列时间依赖建模

3. **多模态特征融合** (`models/multimodal/`)
   - 技术指标特征提取
   - 时间序列模式识别
   - 市场情绪分析
   - 宏观经济因子整合

4. **强化学习环境** (`environment/`)
   - 股票交易环境模拟
   - 奖励函数设计
   - 风险管理机制
   - 交易成本建模

### 技术特点

- **多智能体协作**: 不同智能体专注于不同时间尺度和策略
- **动态分块**: 自适应处理不同长度的时间序列
- **端到端训练**: 从原始数据到交易决策的完整流程
- **风险控制**: 内置风险管理和资金管理机制

## 项目结构

```
rl_stock_trading/
├── agents/                 # DQN智能体实现
│   ├── dqn_agent.py       # 单个DQN智能体
│   ├── multi_agent.py     # 多智能体协调器
│   └── replay_buffer.py   # 经验回放缓冲区
├── models/                 # 神经网络模型
│   ├── dynamic_chunking/   # 动态分块模块
│   ├── multimodal/        # 多模态特征融合
│   └── base_models.py     # 基础网络组件
├── environment/           # 强化学习环境
│   ├── trading_env.py     # 交易环境
│   ├── market_simulator.py # 市场模拟器
│   └── reward_functions.py # 奖励函数
├── data/                  # 数据处理
│   ├── data_loader.py     # 数据加载器
│   ├── feature_engineer.py # 特征工程
│   └── preprocessor.py    # 数据预处理
├── training/              # 训练相关
│   ├── trainer.py         # 训练器
│   ├── evaluator.py       # 评估器
│   └── callbacks.py       # 训练回调
├── utils/                 # 工具函数
│   ├── config.py          # 配置文件
│   ├── metrics.py         # 评估指标
│   └── visualization.py   # 可视化工具
├── scripts/               # 执行脚本
│   ├── train.py           # 训练脚本
│   ├── evaluate.py        # 评估脚本
│   └── trade.py           # 实盘交易脚本
└── tests/                 # 测试文件
```

## 快速开始

### 1. 环境配置
```bash
# 克隆项目
git clone <repository_url>
cd rl_stock_trading

# 安装依赖
pip install -r requirements.txt

# 或者直接安装
pip install -e .
```

### 2. 运行示例
```bash
# 运行完整示例 (包含训练和交易演示)
python scripts/run_example.py
```

### 3. 训练自定义模型
```bash
# 使用您的数据训练模型
python scripts/train.py \
    --data_path your_stock_data.csv \
    --n_episodes 1000 \
    --n_agents 3 \
    --coordination_method attention \
    --initial_capital 1000000
```

### 4. 使用训练好的模型交易
```bash
# 运行交易策略
python scripts/trade.py \
    --model_path checkpoints/your_model \
    --data_path your_stock_data.csv \
    --save_results \
    --plot_results
```

### 5. 数据格式要求
您的CSV数据文件应包含以下列：
- `ts_code`: 股票代码
- `trade_date`: 交易日期 (YYYY-MM-DD格式)
- `close`: 收盘价
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `vol`: 成交量

技术指标列如果缺失会自动计算。

## 核心算法

### DQN多智能体算法
- 使用Double DQN减少过估计偏差
- 优先经验回放提高学习效率
- 多智能体信息共享和协调机制

### 动态分块算法
- 基于注意力机制的自适应分块
- 分层处理不同时间尺度的模式
- 端到端优化分块策略

## 性能指标

- **收益率**: 年化收益率、夏普比率
- **风险指标**: 最大回撤、波动率
- **交易指标**: 胜率、盈亏比
- **强化学习指标**: 累积奖励、探索效率

## 论文参考

1. Tidwell, J. C., & Tidwell, J. S. (2025). Deep Q-Network (DQN) multi-agent reinforcement learning (MARL) for Stock Trading. arXiv:2505.03949.

2. Hwang, S., Wang, B., & Gu, A. (2025). Dynamic Chunking for End-to-End Hierarchical Sequence Modeling. arXiv:2507.07955.

## 高级功能

### 命令行参数详解

#### 训练参数
- `--n_episodes`: 训练轮数 (默认: 1000)
- `--n_agents`: 智能体数量 (默认: 3)
- `--coordination_method`: 协调方法 (attention/voting/weighted)
- `--batch_size`: 批次大小 (默认: 64)
- `--learning_rate`: 学习率 (默认: 0.0001)
- `--lookback_window`: 历史数据窗口 (默认: 20)

#### 交易参数
- `--initial_capital`: 初始资金 (默认: 1000000)
- `--transaction_cost`: 交易成本 (默认: 0.001)
- `--max_position_size`: 最大仓位 (默认: 0.1)

### 模型架构说明

1. **DQN多智能体系统**: 多个DQN智能体协同决策，支持不同的协调策略
2. **动态分块模块**: 自适应处理不同长度的时间序列数据
3. **多模态特征融合**: 整合CNN、LSTM、Transformer和动态分块特征
4. **风险管理**: 内置交易成本、仓位限制和风险控制机制

### 性能优化建议

1. **GPU加速**: 安装CUDA版本的PyTorch以获得更好性能
2. **数据预处理**: 确保数据质量，移除异常值
3. **超参数调优**: 根据具体数据调整学习率、批次大小等参数
4. **模型集成**: 使用多个模型的集成预测提高稳定性

### 故障排除

1. **内存不足**: 减少批次大小或序列长度
2. **训练不收敛**: 调整学习率或增加训练轮数
3. **数据格式错误**: 检查CSV文件格式和列名
4. **模型加载失败**: 确保模型文件路径正确

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
