#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
深度强化学习股票预测系统示例运行脚本
演示完整的训练和交易流程
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import *

def setup_example_environment():
    """设置示例环境"""
    print("=" * 60)
    print("深度强化学习股票预测系统 - 示例运行")
    print("=" * 60)
    print()
    
    # 检查必要的目录
    directories = [DATA_DIR, MODEL_DIR, LOG_DIR, RESULT_DIR]
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")
    
    print("环境设置完成")
    print()

def run_training_example():
    """运行训练示例"""
    print("1. 开始训练示例...")
    print("-" * 40)
    
    # 训练参数
    train_args = [
        sys.executable, "scripts/train.py",
        "--n_episodes", "50",  # 减少训练轮数用于演示
        "--n_agents", "3",
        "--coordination_method", "attention",
        "--batch_size", "32",
        "--learning_rate", "0.0001",
        "--initial_capital", "1000000",
        "--run_id", f"example_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "--log_level", "INFO"
    ]
    
    try:
        # 运行训练
        result = subprocess.run(train_args, 
                              cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                              capture_output=True, 
                              text=True, 
                              timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            print("✓ 训练完成")
            print("训练输出:")
            print(result.stdout[-1000:])  # 显示最后1000个字符
            
            # 查找生成的模型文件
            model_files = []
            for file in os.listdir(MODEL_DIR):
                if file.startswith("example_") and file.endswith("_best_agent_0.pth"):
                    model_files.append(file)
            
            if model_files:
                latest_model = sorted(model_files)[-1]
                model_path = os.path.join(MODEL_DIR, latest_model.replace("_agent_0.pth", ""))
                print(f"找到训练好的模型: {model_path}")
                return model_path
            else:
                print("警告: 未找到训练好的模型文件")
                return None
        else:
            print("✗ 训练失败")
            print("错误输出:")
            print(result.stderr)
            return None
            
    except subprocess.TimeoutExpired:
        print("✗ 训练超时")
        return None
    except Exception as e:
        print(f"✗ 训练过程中发生错误: {str(e)}")
        return None

def run_trading_example(model_path):
    """运行交易示例"""
    print("\n2. 开始交易示例...")
    print("-" * 40)
    
    if model_path is None:
        print("跳过交易示例 (没有可用的模型)")
        return
    
    # 交易参数
    trade_args = [
        sys.executable, "scripts/trade.py",
        "--model_path", model_path,
        "--initial_capital", "1000000",
        "--save_results",
        "--plot_results",
        "--output_dir", RESULT_DIR
    ]
    
    try:
        # 运行交易
        result = subprocess.run(trade_args,
                              cwd=os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                              capture_output=True,
                              text=True,
                              timeout=600)  # 10分钟超时
        
        if result.returncode == 0:
            print("✓ 交易完成")
            print("交易输出:")
            print(result.stdout[-1000:])  # 显示最后1000个字符
        else:
            print("✗ 交易失败")
            print("错误输出:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("✗ 交易超时")
    except Exception as e:
        print(f"✗ 交易过程中发生错误: {str(e)}")

def run_quick_test():
    """运行快速测试"""
    print("\n3. 运行快速功能测试...")
    print("-" * 40)
    
    try:
        # 测试各个模块
        test_modules = [
            "utils.config",
            "agents.dqn_agent", 
            "agents.multi_agent",
            "models.dynamic_chunking.chunking_module",
            "models.multimodal.feature_fusion",
            "environment.trading_env",
            "environment.reward_functions",
            "data.data_loader",
            "utils.metrics"
        ]
        
        for module in test_modules:
            try:
                __import__(module)
                print(f"✓ {module}")
            except Exception as e:
                print(f"✗ {module}: {str(e)}")
        
        print("\n模块测试完成")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

def show_system_info():
    """显示系统信息"""
    print("\n4. 系统信息:")
    print("-" * 40)
    
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            print(f"当前GPU: {torch.cuda.get_device_name()}")
    except ImportError:
        print("PyTorch未安装")
    
    try:
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
    except ImportError:
        print("Pandas未安装")
    
    try:
        import numpy as np
        print(f"NumPy版本: {np.__version__}")
    except ImportError:
        print("NumPy未安装")
    
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")

def show_usage_instructions():
    """显示使用说明"""
    print("\n5. 使用说明:")
    print("-" * 40)
    print("""
基本使用流程:

1. 训练模型:
   python scripts/train.py --data_path your_data.csv --n_episodes 1000

2. 使用模型交易:
   python scripts/trade.py --model_path checkpoints/your_model --data_path your_data.csv

3. 主要参数说明:
   --n_episodes: 训练轮数
   --n_agents: 智能体数量 
   --coordination_method: 协调方法 (attention/voting/weighted)
   --initial_capital: 初始资金
   --learning_rate: 学习率
   --batch_size: 批次大小

4. 数据格式要求:
   - CSV文件，包含以下列: ts_code, trade_date, close, open, high, low, vol
   - 技术指标列 (如果没有会自动计算)
   - 日期格式: YYYY-MM-DD

5. 输出文件:
   - 模型文件: checkpoints/
   - 训练日志: logs/
   - 结果文件: results/
   - TensorBoard日志: logs/tensorboard/

6. 系统特点:
   - DQN多智能体协作决策
   - 动态分块序列建模
   - 多模态特征融合
   - 风险管理和交易成本控制
   - 完整的性能评估体系
""")

def main():
    """主函数"""
    try:
        # 设置环境
        setup_example_environment()
        
        # 显示系统信息
        show_system_info()
        
        # 运行快速测试
        run_quick_test()
        
        # 询问是否运行完整示例
        print("\n" + "=" * 60)
        response = input("是否运行完整的训练和交易示例? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            print("\n开始运行完整示例...")
            
            # 运行训练示例
            model_path = run_training_example()
            
            # 运行交易示例
            run_trading_example(model_path)
            
            print("\n" + "=" * 60)
            print("示例运行完成!")
            
            # 显示结果文件
            print("\n生成的文件:")
            for root, dirs, files in os.walk(RESULT_DIR):
                for file in files:
                    print(f"  {os.path.join(root, file)}")
        
        # 显示使用说明
        show_usage_instructions()
        
        print("\n" + "=" * 60)
        print("感谢使用深度强化学习股票预测系统!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n运行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
