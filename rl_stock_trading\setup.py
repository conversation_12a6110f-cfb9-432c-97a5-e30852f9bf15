#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
深度强化学习股票预测系统安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "深度强化学习股票预测系统"

# 读取requirements文件
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

setup(
    name="rl-stock-trading",
    version="1.0.0",
    author="AI Assistant",
    author_email="<EMAIL>",
    description="基于深度强化学习的股票预测和交易系统",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/example/rl-stock-trading",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Office/Business :: Financial :: Investment",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=3.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
        ],
        "gpu": [
            "cupy-cuda11x>=11.0.0",
        ],
        "acceleration": [
            "numba>=0.56.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "rl-stock-train=scripts.train:main",
            "rl-stock-trade=scripts.trade:main",
            "rl-stock-example=scripts.run_example:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.txt", "*.md", "*.yaml", "*.yml"],
    },
    zip_safe=False,
)
