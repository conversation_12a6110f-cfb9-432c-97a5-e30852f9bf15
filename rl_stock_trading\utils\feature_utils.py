# -*- coding: utf-8 -*-
"""
特征处理工具函数
避免代码重复，提供统一的特征处理接口
"""

import torch
import numpy as np
from typing import Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *

class FeatureProcessor:
    """特征处理器"""
    
    def __init__(self, feature_fusion_model=None):
        """
        初始化特征处理器
        
        Args:
            feature_fusion_model: 多模态特征融合模型
        """
        self.feature_fusion_model = feature_fusion_model
    
    def extract_enhanced_features(self, state: np.ndarray) -> np.ndarray:
        """
        提取增强特征
        
        Args:
            state: 原始状态
            
        Returns:
            增强后的状态特征
        """
        # 将状态转换为时间序列格式
        n_features = len(TECHNICAL_FACTORS)
        seq_len = LOOKBACK_WINDOW
        
        # 提取技术指标部分
        tech_features = state[:n_features * seq_len]
        tech_features = tech_features.reshape(seq_len, n_features)
        
        # 如果有特征融合模型，使用它
        if self.feature_fusion_model is not None:
            # 转换为张量
            tech_tensor = torch.FloatTensor(tech_features).unsqueeze(0).to(DEVICE)
            
            # 通过多模态特征融合
            with torch.no_grad():
                fused_features, _ = self.feature_fusion_model(tech_tensor)
                fused_features = fused_features.cpu().numpy().flatten()
        else:
            # 如果没有特征融合模型，使用简单的特征处理
            fused_features = self._simple_feature_processing(tech_features)
        
        # 拼接投资组合状态
        portfolio_features = state[n_features * seq_len:]
        
        # 组合增强特征
        enhanced_state = np.concatenate([fused_features, portfolio_features])
        
        return enhanced_state
    
    def _simple_feature_processing(self, tech_features: np.ndarray) -> np.ndarray:
        """
        简单的特征处理（当没有特征融合模型时使用）
        
        Args:
            tech_features: 技术特征 [seq_len, n_features]
            
        Returns:
            处理后的特征
        """
        # 计算统计特征
        mean_features = np.mean(tech_features, axis=0)
        std_features = np.std(tech_features, axis=0)
        max_features = np.max(tech_features, axis=0)
        min_features = np.min(tech_features, axis=0)
        
        # 计算趋势特征
        if tech_features.shape[0] > 1:
            trend_features = tech_features[-1] - tech_features[0]
        else:
            trend_features = np.zeros(tech_features.shape[1])
        
        # 拼接所有特征
        processed_features = np.concatenate([
            mean_features,
            std_features, 
            max_features,
            min_features,
            trend_features
        ])
        
        return processed_features
    
    def validate_state_shape(self, state: np.ndarray) -> bool:
        """
        验证状态形状是否正确
        
        Args:
            state: 状态数组
            
        Returns:
            是否有效
        """
        expected_tech_features = len(TECHNICAL_FACTORS) * LOOKBACK_WINDOW
        
        if len(state) < expected_tech_features:
            return False
        
        return True
    
    def normalize_features(self, features: np.ndarray, 
                          feature_stats: Optional[dict] = None) -> np.ndarray:
        """
        标准化特征
        
        Args:
            features: 特征数组
            feature_stats: 特征统计信息 (mean, std)
            
        Returns:
            标准化后的特征
        """
        if feature_stats is None:
            # 使用当前特征的统计信息
            mean = np.mean(features)
            std = np.std(features)
        else:
            mean = feature_stats.get('mean', 0)
            std = feature_stats.get('std', 1)
        
        if std > 1e-8:
            normalized = (features - mean) / std
        else:
            normalized = features - mean
        
        return normalized
    
    def extract_portfolio_features(self, state: np.ndarray) -> np.ndarray:
        """
        提取投资组合特征
        
        Args:
            state: 完整状态
            
        Returns:
            投资组合特征
        """
        n_features = len(TECHNICAL_FACTORS)
        seq_len = LOOKBACK_WINDOW
        
        # 投资组合特征从技术特征之后开始
        portfolio_start = n_features * seq_len
        portfolio_features = state[portfolio_start:]
        
        return portfolio_features
    
    def extract_technical_features(self, state: np.ndarray) -> np.ndarray:
        """
        提取技术特征
        
        Args:
            state: 完整状态
            
        Returns:
            技术特征 [seq_len, n_features]
        """
        n_features = len(TECHNICAL_FACTORS)
        seq_len = LOOKBACK_WINDOW
        
        # 提取技术指标部分
        tech_features = state[:n_features * seq_len]
        tech_features = tech_features.reshape(seq_len, n_features)
        
        return tech_features

# 全局特征处理器实例
_global_feature_processor = None

def get_feature_processor(feature_fusion_model=None) -> FeatureProcessor:
    """
    获取全局特征处理器实例
    
    Args:
        feature_fusion_model: 特征融合模型
        
    Returns:
        特征处理器实例
    """
    global _global_feature_processor
    
    if _global_feature_processor is None or feature_fusion_model is not None:
        _global_feature_processor = FeatureProcessor(feature_fusion_model)
    
    return _global_feature_processor

def extract_enhanced_features(state: np.ndarray, 
                            feature_fusion_model=None) -> np.ndarray:
    """
    便捷函数：提取增强特征
    
    Args:
        state: 原始状态
        feature_fusion_model: 特征融合模型
        
    Returns:
        增强后的特征
    """
    processor = get_feature_processor(feature_fusion_model)
    return processor.extract_enhanced_features(state)

if __name__ == "__main__":
    # 测试代码
    print("测试特征处理工具...")
    
    # 创建模拟状态
    n_features = len(TECHNICAL_FACTORS)
    seq_len = LOOKBACK_WINDOW
    portfolio_features = 5  # 假设5个投资组合特征
    
    state = np.random.randn(n_features * seq_len + portfolio_features)
    
    # 测试特征处理
    processor = FeatureProcessor()
    
    # 验证状态形状
    is_valid = processor.validate_state_shape(state)
    print(f"状态形状有效: {is_valid}")
    
    # 提取技术特征
    tech_features = processor.extract_technical_features(state)
    print(f"技术特征形状: {tech_features.shape}")
    
    # 提取投资组合特征
    portfolio_feats = processor.extract_portfolio_features(state)
    print(f"投资组合特征形状: {portfolio_feats.shape}")
    
    # 提取增强特征
    enhanced_features = processor.extract_enhanced_features(state)
    print(f"增强特征形状: {enhanced_features.shape}")
    
    print("✓ 特征处理工具测试通过")
