# -*- coding: utf-8 -*-
"""
Deep Q-Network (DQN) 智能体实现
支持Double DQN、Dueling DQN和优先经验回放
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import random
from typing import Tuple, List, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *
from agents.replay_buffer import PrioritizedReplayBuffer, Experience, collate_experiences

class DuelingDQN(nn.Module):
    """
    Dueling DQN网络架构
    分离状态价值和动作优势的估计
    """
    def __init__(self, 
                 state_dim: int = STATE_DIM,
                 action_dim: int = ACTION_SPACE_SIZE,
                 hidden_dims: List[int] = DQN_HIDDEN_DIMS,
                 dropout: float = DQN_DROPOUT):
        super(DuelingDQN, self).__init__()
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 共享特征提取层
        layers = []
        input_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout)
            ])
            input_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # 状态价值流
        self.value_stream = nn.Sequential(
            nn.Linear(hidden_dims[-1], hidden_dims[-1] // 2),
            nn.ReLU(),
            nn.Linear(hidden_dims[-1] // 2, 1)
        )
        
        # 动作优势流
        self.advantage_stream = nn.Sequential(
            nn.Linear(hidden_dims[-1], hidden_dims[-1] // 2),
            nn.ReLU(),
            nn.Linear(hidden_dims[-1] // 2, action_dim)
        )
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """权重初始化"""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            nn.init.constant_(module.bias, 0)
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            state: 状态张量 [batch_size, state_dim]
            
        Returns:
            Q值 [batch_size, action_dim]
        """
        features = self.feature_extractor(state)
        
        # 计算状态价值
        value = self.value_stream(features)
        
        # 计算动作优势
        advantage = self.advantage_stream(features)
        
        # Dueling架构：Q(s,a) = V(s) + A(s,a) - mean(A(s,a))
        q_values = value + advantage - advantage.mean(dim=1, keepdim=True)
        
        return q_values

class DQNAgent:
    """
    DQN智能体实现
    支持Double DQN、Dueling DQN、优先经验回放
    """
    def __init__(self, 
                 agent_id: int = 0,
                 state_dim: int = STATE_DIM,
                 action_dim: int = ACTION_SPACE_SIZE,
                 learning_rate: float = LEARNING_RATE,
                 gamma: float = GAMMA,
                 epsilon_start: float = EPSILON_START,
                 epsilon_end: float = EPSILON_END,
                 epsilon_decay: float = EPSILON_DECAY,
                 tau: float = TAU,
                 use_prioritized_replay: bool = True):
        """
        初始化DQN智能体
        
        Args:
            agent_id: 智能体ID
            state_dim: 状态维度
            action_dim: 动作维度
            learning_rate: 学习率
            gamma: 折扣因子
            epsilon_start: 探索率起始值
            epsilon_end: 探索率终止值
            epsilon_decay: 探索率衰减
            tau: 软更新参数
            use_prioritized_replay: 是否使用优先经验回放
        """
        self.agent_id = agent_id
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma
        self.tau = tau
        
        # 探索策略
        self.epsilon = epsilon_start
        self.epsilon_end = epsilon_end
        self.epsilon_decay = epsilon_decay
        
        # 神经网络
        self.q_network = DuelingDQN(state_dim, action_dim).to(DEVICE)
        self.target_network = DuelingDQN(state_dim, action_dim).to(DEVICE)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)
        
        # 初始化目标网络
        self.hard_update()
        
        # 经验回放缓冲区
        if use_prioritized_replay:
            self.replay_buffer = PrioritizedReplayBuffer()
        else:
            from agents.replay_buffer import SimpleReplayBuffer
            self.replay_buffer = SimpleReplayBuffer()
        
        self.use_prioritized_replay = use_prioritized_replay
        
        # 训练统计
        self.training_step = 0
        self.episode_rewards = []
        self.losses = []
    
    def select_action(self, state: np.ndarray, training: bool = True) -> int:
        """
        选择动作（epsilon-greedy策略）
        
        Args:
            state: 当前状态
            training: 是否为训练模式
            
        Returns:
            选择的动作
        """
        if training and random.random() < self.epsilon:
            # 随机探索
            return random.randint(0, self.action_dim - 1)
        else:
            # 贪婪选择
            with torch.no_grad():
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(DEVICE)
                q_values = self.q_network(state_tensor)
                return q_values.argmax().item()
    
    def store_experience(self, state: np.ndarray, action: int, reward: float, 
                        next_state: np.ndarray, done: bool):
        """
        存储经验
        
        Args:
            state: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_state: 下一状态
            done: 是否结束
        """
        experience = Experience(state, action, reward, next_state, done)
        
        if self.use_prioritized_replay:
            # 计算TD误差作为优先级
            with torch.no_grad():
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(DEVICE)
                next_state_tensor = torch.FloatTensor(next_state).unsqueeze(0).to(DEVICE)
                
                current_q = self.q_network(state_tensor)[0, action]
                next_q = self.target_network(next_state_tensor).max(1)[0]
                target_q = reward + (self.gamma * next_q * (1 - done))
                
                td_error = abs(current_q - target_q).item()
                
            self.replay_buffer.add(td_error, experience)
        else:
            self.replay_buffer.add(experience)
    
    def train(self, batch_size: int = BATCH_SIZE) -> Optional[float]:
        """
        训练智能体
        
        Args:
            batch_size: 批次大小
            
        Returns:
            损失值
        """
        if len(self.replay_buffer) < MIN_REPLAY_SIZE:
            return None
        
        # 采样经验
        if self.use_prioritized_replay:
            experiences, indices, weights = self.replay_buffer.sample(batch_size)
            weights = torch.FloatTensor(weights).to(DEVICE)
        else:
            experiences = self.replay_buffer.sample(batch_size)
            weights = torch.ones(batch_size).to(DEVICE)
            indices = None
        
        # 转换为张量
        states, actions, rewards, next_states, dones = collate_experiences(experiences)
        
        # 计算当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Double DQN: 使用主网络选择动作，目标网络评估价值
        with torch.no_grad():
            next_actions = self.q_network(next_states).argmax(1)
            next_q_values = self.target_network(next_states).gather(1, next_actions.unsqueeze(1))
            target_q_values = rewards.unsqueeze(1) + (self.gamma * next_q_values * (~dones).unsqueeze(1))
        
        # 计算TD误差
        td_errors = current_q_values - target_q_values
        
        # 计算加权损失
        loss = (weights * td_errors.squeeze().pow(2)).mean()
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), max_norm=1.0)
        
        self.optimizer.step()
        
        # 更新优先级
        if self.use_prioritized_replay and indices is not None:
            td_errors_np = td_errors.detach().cpu().numpy().flatten()
            self.replay_buffer.update(indices, td_errors_np)
        
        # 软更新目标网络
        self.soft_update()
        
        # 更新探索率
        self.epsilon = max(self.epsilon_end, self.epsilon * self.epsilon_decay)
        
        # 记录统计信息
        self.training_step += 1
        loss_value = loss.item()
        self.losses.append(loss_value)
        
        return loss_value
    
    def soft_update(self):
        """软更新目标网络"""
        for target_param, local_param in zip(self.target_network.parameters(), 
                                           self.q_network.parameters()):
            target_param.data.copy_(self.tau * local_param.data + 
                                  (1.0 - self.tau) * target_param.data)
    
    def hard_update(self):
        """硬更新目标网络"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def save(self, filepath: str):
        """保存模型"""
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'training_step': self.training_step,
            'agent_id': self.agent_id
        }, filepath)
    
    def load(self, filepath: str):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=DEVICE)
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.training_step = checkpoint['training_step']
        self.agent_id = checkpoint['agent_id']

    def get_q_values(self, state: np.ndarray) -> torch.Tensor:
        """
        获取Q值（用于多智能体协调）

        Args:
            state: 状态

        Returns:
            Q值张量
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(DEVICE)
            return self.q_network(state_tensor)

    def get_training_stats(self) -> dict:
        """获取训练统计信息"""
        return {
            'agent_id': self.agent_id,
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'buffer_size': len(self.replay_buffer),
            'avg_loss': np.mean(self.losses[-100:]) if self.losses else 0.0,
            'avg_reward': np.mean(self.episode_rewards[-10:]) if self.episode_rewards else 0.0
        }

if __name__ == "__main__":
    # 测试代码
    print("测试DQN智能体...")

    agent = DQNAgent(agent_id=0)

    # 测试动作选择
    state = np.random.randn(STATE_DIM)
    action = agent.select_action(state)
    print(f"选择的动作: {action}")

    # 测试经验存储
    next_state = np.random.randn(STATE_DIM)
    agent.store_experience(state, action, 1.0, next_state, False)

    print(f"经验缓冲区大小: {len(agent.replay_buffer)}")
    print("✓ DQN智能体测试通过")
