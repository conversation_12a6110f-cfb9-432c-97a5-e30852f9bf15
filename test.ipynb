{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       ts_code trade_date  bond_value      open      high       low     close\n", "0    110092.SH   20250619       103.1    87.000    87.097    85.712    85.792\n", "1    127033.SZ   20250619        59.7    86.172    86.601    85.861    86.004\n", "2    118031.SH   20250619       110.5    98.450    98.730    97.995    98.100\n", "3    118027.SH   20250619        79.1    99.200    99.399    98.180    98.263\n", "4    127089.SZ   20250619       103.9    99.620    99.700    99.164    99.327\n", "..         ...        ...         ...       ...       ...       ...       ...\n", "465  127037.SZ   20250619       107.7   268.168   274.900   266.200   267.300\n", "466  127081.SZ   20250619        95.5   316.000   330.998   309.400   309.800\n", "467  111012.SH   20250619       100.5   346.576   355.999   344.000   344.380\n", "468  113615.SH   20250619       110.6   367.419   370.046   357.636   363.688\n", "469  123118.SZ   20250619       108.4  1755.000  1763.888  1692.000  1696.000\n", "\n", "[470 rows x 7 columns]\n"]}], "source": ["\n", "import tushare as ts\n", "\n", "\n", "TUSHARE_TOKEN = '28af7cb8a5cfd2468fdbc7649f1ed354cd2fd1ae4d7ceafe4a3cfecd'\n", "\n", "pro = ts.pro_api(TUSHARE_TOKEN)\n", "#获取可转债行情\n", "df = pro.cb_daily(trade_date='20250619', fields='ts_code,trade_date, bond_value,open,high,low,close')\n", "print(df)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-05-08 21:49:55,202 - INFO - Tushare Pro API 初始化成功。\n", "2025-05-08 21:49:55,202 - INFO - --- 开始执行数据下载脚本 ---\n", "2025-05-08 21:49:55,202 - INFO - 配置: MAX_WORKERS=8, INTER_CALL_DELAY=0.15\n", "2025-05-08 21:49:55,202 - INFO - 下载日期范围: 20250430 到 20250508\n", "2025-05-08 21:49:55,202 - INFO - CB_DAILY 请求字段: ts_code,trade_date,pre_close,open,high,low,close,change,pct_chg,vol,amount,bond_value,bond_over_rate,cb_value,cb_over_rate\n", "2025-05-08 21:49:55,202 - INFO - DAILY_BASIC 请求字段: ts_code,trade_date,dv_ttm\n", "2025-05-08 21:49:55,202 - INFO - 正在获取可转债基本信息...\n", "2025-05-08 21:49:56,033 - INFO - 已覆盖保存 1049 行数据到 cb_basic.csv\n", "2025-05-08 21:49:56,052 - INFO - 根据到期日筛选：当前日期 20250508\n", "2025-05-08 21:49:56,052 - INFO - 找到 507 个尚未到期的可转债, 542 个已到期的可转债。\n", "2025-05-08 21:49:56,052 - INFO - 清理可转债日线数据中的已到期记录...\n", "2025-05-08 21:50:00,722 - INFO - 已覆盖保存 336504 行数据到 cb_daily.csv\n", "2025-05-08 21:50:00,722 - INFO - 已从 cb_daily.csv 中清理 0 行已到期的数据。\n", "2025-05-08 21:50:00,729 - INFO - 清理股票日线数据中与已到期可转债关联的正股记录...\n", "2025-05-08 21:50:07,300 - INFO - 已覆盖保存 587934 行数据到 daily.csv\n", "2025-05-08 21:50:07,306 - INFO - 已从 daily.csv 中清理 0 行已到期的数据。\n", "2025-05-08 21:50:07,312 - INFO - --- 开始下载更新尚未到期的可转债日线数据 ---\n", "2025-05-08 21:50:07,854 - INFO - 从 cb_daily.csv 读取了 479 个代码的最新日期。\n", "2025-05-08 21:50:07,871 - INFO - 为 cb_daily.csv 确定了 507 个下载任务。\n", "2025-05-08 21:50:07,871 - INFO - 需要下载 507 个可转债日线任务。\n", "2025-05-08 21:50:10,046 - INFO - [CB阶段] 下载进度: 50/507 个任务已完成。\n", "2025-05-08 21:50:11,957 - INFO - [CB阶段] 下载进度: 100/507 个任务已完成。\n", "2025-05-08 21:50:13,806 - INFO - [CB阶段] 下载进度: 150/507 个任务已完成。\n", "2025-05-08 21:50:15,615 - INFO - [CB阶段] 下载进度: 200/507 个任务已完成。\n", "2025-05-08 21:50:17,690 - INFO - [CB阶段] 下载进度: 250/507 个任务已完成。\n", "2025-05-08 21:50:19,586 - INFO - [CB阶段] 下载进度: 300/507 个任务已完成。\n", "2025-05-08 21:50:21,479 - INFO - [CB阶段] 下载进度: 350/507 个任务已完成。\n", "2025-05-08 21:50:23,390 - INFO - [CB阶段] 下载进度: 400/507 个任务已完成。\n", "2025-05-08 21:50:25,050 - WARNING - 已达到API调用频率限制 (450/分钟). 等待 30.25 秒...\n", "2025-05-08 21:50:55,307 - WARNING - 已达到API调用频率限制 (450/分钟). 等待 12.66 秒...\n", "2025-05-08 21:50:55,604 - INFO - [CB阶段] 下载进度: 450/507 个任务已完成。\n", "2025-05-08 21:51:10,149 - INFO - [CB阶段] 下载进度: 500/507 个任务已完成。\n", "2025-05-08 21:51:10,461 - INFO - [CB阶段] 下载进度: 507/507 个任务已完成。\n", "2025-05-08 21:51:10,461 - INFO - [CB阶段] 所有下载任务完成，耗时: 62.59 秒。\n", "2025-05-08 21:51:10,461 - INFO - [CB阶段] 开始合并和保存数据...\n", "2025-05-08 21:51:10,746 - INFO - 追加了 475 行数据到 cb_daily.csv\n", "2025-05-08 21:51:10,746 - INFO - --- 结束可转债日线数据处理 ---\n", "2025-05-08 21:51:10,747 - INFO - --- 开始下载更新正股日线数据 ---\n", "2025-05-08 21:51:11,444 - INFO - 从 daily.csv 读取了 488 个代码的最新日期。\n", "2025-05-08 21:51:11,461 - INFO - 为 daily.csv 确定了 489 个下载任务。\n", "2025-05-08 21:51:11,461 - INFO - 需要下载 489 个股票日线任务。\n", "2025-05-08 21:51:13,578 - INFO - [Stock阶段] 下载进度: 50/489 个任务已完成。\n", "2025-05-08 21:51:15,515 - INFO - [Stock阶段] 下载进度: 100/489 个任务已完成。\n", "2025-05-08 21:51:17,409 - INFO - [Stock阶段] 下载进度: 150/489 个任务已完成。\n", "2025-05-08 21:51:19,220 - INFO - [Stock阶段] 下载进度: 200/489 个任务已完成。\n", "2025-05-08 21:51:21,106 - INFO - [Stock阶段] 下载进度: 250/489 个任务已完成。\n", "2025-05-08 21:51:23,129 - INFO - [Stock阶段] 下载进度: 300/489 个任务已完成。\n", "2025-05-08 21:51:24,974 - INFO - [Stock阶段] 下载进度: 350/489 个任务已完成。\n", "2025-05-08 21:51:26,315 - WARNING - 已达到API调用频率限制 (450/分钟). 等待 29.09 秒...\n", "2025-05-08 21:51:55,425 - WARNING - 已达到API调用频率限制 (450/分钟). 等待 12.66 秒...\n", "2025-05-08 21:52:08,441 - INFO - [Stock阶段] 下载进度: 400/489 个任务已完成。\n", "2025-05-08 21:52:10,266 - WARNING - 已达到API调用频率限制 (450/分钟). 等待 1.30 秒...\n", "2025-05-08 21:52:10,552 - INFO - [Stock阶段] 下载进度: 450/489 个任务已完成。\n", "2025-05-08 21:52:13,276 - INFO - [Stock阶段] 下载进度: 489/489 个任务已完成。\n", "2025-05-08 21:52:13,276 - INFO - [Stock阶段] 所有下载任务完成，耗时: 61.81 秒。\n", "2025-05-08 21:52:13,276 - INFO - [Stock阶段] 开始合并和保存新下载的股票日线数据...\n", "2025-05-08 21:52:13,441 - INFO - 追加了 487 行数据到 daily.csv\n", "2025-05-08 21:52:13,442 - INFO - --- 结束股票日线数据处理 ---\n", "2025-05-08 21:52:13,442 - INFO - --- 开始处理股票日线基础信息 (含 dv_ttm) ---\n", "2025-05-08 21:52:13,442 - INFO - [DailyBasic阶段] 读取股票日线文件以确定需要下载的 dv_ttm 范围...\n", "2025-05-08 21:52:14,337 - INFO - [DailyBasic阶段] 现有股票日线数据共 588421 行。\n", "2025-05-08 21:52:15,036 - INFO - 从 daily.csv 读取了 488 个代码的最新日期。\n", "2025-05-08 21:52:15,052 - INFO - 为 daily.csv 确定了 2 个下载任务。\n", "2025-05-08 21:52:15,054 - INFO - [DailyBasic阶段] 需要下载 2 个股票日线基础信息任务。\n", "2025-05-08 21:52:15,368 - INFO - [DailyBasic阶段] 下载进度: 2/2 个任务已完成。\n", "2025-05-08 21:52:15,369 - INFO - [DailyBasic阶段] 所有下载任务完成，耗时: 0.32 秒。\n", "2025-05-08 21:52:15,369 - INFO - [DailyBasic阶段] 没有下载到新的日线基础信息数据。\n", "2025-05-08 21:52:15,369 - INFO - [DailyBasic阶段] 保存最终合并了 dv_ttm 的数据...\n", "2025-05-08 21:52:24,486 - INFO - 已覆盖保存 588421 行数据到 daily.csv\n", "2025-05-08 21:52:24,495 - INFO - --- 结束股票日线基础信息处理 ---\n", "2025-05-08 21:52:24,495 - INFO - --- 开始处理 Shibor 利率数据 (shibor_rate.csv) ---\n", "2025-05-08 21:52:24,606 - INFO - 从 shibor_rate.csv 读取到最新日期: 20250507。\n", "2025-05-08 21:52:24,607 - INFO - 为 shibor_rate.csv 确定下载范围: 20250508 到 20250508\n", "2025-05-08 21:52:24,607 - INFO - 正在获取 Shibor 利率数据从 20250508 到 20250508\n", "2025-05-08 21:52:24,893 - INFO - 成功获取 1 条 Shibor 利率数据。\n", "2025-05-08 21:52:24,896 - INFO - 追加了 1 行数据到 shibor_rate.csv\n", "2025-05-08 21:52:24,896 - INFO - --- 结束 <PERSON><PERSON> 利率数据处理 ---\n", "2025-05-08 21:52:24,896 - INFO - === 数据处理总结 ===\n", "2025-05-08 21:52:24,896 - INFO - - 总共处理了 1049 条可转债基本信息\n", "2025-05-08 21:52:24,896 - INFO - - 找到 507 个尚未到期的可转债\n", "2025-05-08 21:52:24,896 - INFO - - 找到 542 个已到期的可转债\n", "2025-05-08 21:52:24,896 - INFO - - 保留了 489 个正股代码的数据\n", "2025-05-08 21:52:25,370 - INFO - - 可转债日线数据现有 336979 行\n", "2025-05-08 21:52:26,022 - INFO - - 股票日线数据现有 588421 行\n", "2025-05-08 21:52:26,038 - INFO - - <PERSON><PERSON>利率数据现有 1318 行\n", "2025-05-08 21:52:26,038 - INFO - --- 脚本执行完毕，总耗时: 150.84 秒 ---\n"]}], "source": ["!python download_test.py"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据已保存到：akshare_data/cb_basic.csv\n"]}], "source": ["import os\n", "import akshare as ak\n", "\n", "# 1. 设置根目录（例如 D:/data/）\n", "root_dir = \"akshare_data/\"  # 替换成你的实际路径\n", "\n", "# 2. 检查目录是否存在，不存在则创建\n", "if not os.path.exists(root_dir):\n", "    os.makedirs(root_dir)\n", "\n", "# 3. 获取可转债数据\n", "bond_zh_cov_info_ths_df = ak.bond_zh_cov_info_ths()\n", "\n", "# 4. 定义 CSV 文件路径\n", "csv_path = os.path.join(root_dir, \"cb_basic.csv\")\n", "\n", "# 5. 保存为 CSV（UTF-8 编码，避免中文乱码）\n", "bond_zh_cov_info_ths_df.to_csv(csv_path, index=False, encoding=\"utf_8_sig\")\n", "\n", "print(f\"数据已保存到：{csv_path}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["            日期      收盘价       纯债价值       转股价值      纯债溢价率      转股溢价率\n", "0   2025-04-09  100.000  97.964972  89.373098   2.077302  11.890493\n", "1   2025-04-10  100.000  97.937685  88.399270   2.105742  13.123107\n", "2   2025-04-11  100.000  97.945301  95.045648   2.097803   5.212602\n", "3   2025-04-14  100.000  97.921213  93.925746   2.122918   6.467081\n", "4   2025-04-15  100.000  97.898965  91.819842   2.146126   8.908922\n", "5   2025-04-16  100.000  97.943534  91.418138   2.099644   9.387483\n", "6   2025-04-17  100.000  97.979366  91.941570   2.062305   8.764729\n", "7   2025-04-18  100.000  97.899196  92.562386   2.145885   8.035245\n", "8   2025-04-21  100.000  97.864637  97.175898   2.181956   2.906176\n", "9   2025-04-22  100.000  97.846246  97.236762   2.201161   2.841763\n", "10  2025-04-23  100.000  97.814493  96.311625   2.234339   3.829626\n", "11  2025-04-24  100.000  97.820771  91.174680   2.227778   9.679573\n", "12  2025-04-25  100.000  97.740584  92.635423   2.311646   7.950066\n", "13  2025-04-28      NaN  97.775462  97.285453   2.275150   2.790290\n", "14  2025-04-29      NaN  97.839571  93.268411   2.208134   7.217437\n", "15  2025-04-30  127.610  97.908758  96.092514  30.335633  32.799107\n", "16  2025-05-06  129.696  98.005971  95.909921  32.334794  35.226887\n", "17  2025-05-07  129.016  98.011197  93.840536  31.633940  37.484296\n", "18  2025-05-08  129.342  98.095801  93.852708  31.852738  37.813817\n", "19  2025-05-09  128.011  98.057052  87.827145  30.547470  45.753342\n", "20  2025-05-12  128.269  98.115068  87.961047  30.733232  45.824777\n", "21  2025-05-13  128.273  98.120651  86.500304  30.729871  48.291964\n", "22  2025-05-14  128.003  98.103698  86.110773  30.477242  48.649229\n", "23  2025-05-15  126.452  98.133186  83.858795  28.857530  50.791578\n", "24  2025-05-16  125.992  98.149356  84.175289  28.367628  49.678132\n", "25  2025-05-19  125.921  98.175523  84.637858  28.261095  48.776214\n"]}], "source": ["import akshare as ak\n", "\n", "bond_zh_cov_value_analysis_df = ak.bond_zh_cov_value_analysis(symbol=\"118055\")\n", "print(bond_zh_cov_value_analysis_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import akshare as ak\n", "\n", "bond_zh_hs_cov_spot_df = ak.bond_zh_hs_cov_spot()\n", "bond_zh_hs_cov_spot_df.to\n", "print(bond_zh_hs_cov_spot_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54815edbb7ef4294bacb2a935e32d013", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["数据已保存到：akshare_data/cb_daily.csv\n"]}], "source": ["import os\n", "import akshare as ak\n", "\n", "# 1. 设置根目录（例如 D:/data/）\n", "root_dir = \"akshare_data/\"  # 替换成你的实际路径\n", "\n", "# 2. 检查目录是否存在，不存在则创建\n", "if not os.path.exists(root_dir):\n", "    os.makedirs(root_dir)\n", "\n", "# 3. 获取可转债数据\n", "bond_zh_hs_cov_spot_df = ak.bond_zh_hs_cov_spot()\n", "\n", "# 4. 定义 CSV 文件路径\n", "csv_path = os.path.join(root_dir, \"cb_daily.csv\")\n", "\n", "# 5. 保存为 CSV（UTF-8 编码，避免中文乱码）\n", "bond_zh_hs_cov_spot_df.to_csv(csv_path, index=False, encoding=\"utf_8_sig\")\n", "\n", "print(f\"数据已保存到：{csv_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import akshare as ak\n", "import pandas as pd\n", "from datetime import datetime\n", "import os\n", "import re\n", "\n", "def convert_symbol_format(symbol):\n", "    \"\"\"转换实时格式(110059.SH)为历史格式(sh110059)\"\"\"\n", "    if '.' in symbol:\n", "        code, market = symbol.split('.')\n", "        return (market.lower() + code)\n", "    return symbol\n", "\n", "def convert_historical_to_akshare(ts_code):\n", "    \"\"\"转换历史格式(sh110059)为akshare分析格式(113527)\"\"\"\n", "    # 提取数字部分\n", "    match = re.search(r'(\\d+)', ts_code)\n", "    if match:\n", "        return match.group(1)\n", "    return ts_code\n", "\n", "def get_today_date_str():\n", "    \"\"\"获取今天的日期，格式为YYYYMMDD\"\"\"\n", "    return datetime.now().strftime('%Y%m%d')\n", "\n", "def fetch_and_merge_data(historical_csv_path):\n", "    # 步骤1：获取实时可转债数据\n", "    print(\"正在获取实时可转债数据...\")\n", "    try:\n", "        bond_real_time_df = ak.bond_zh_hs_cov_spot()\n", "        print(f\"成功获取{len(bond_real_time_df)}条实时可转债记录\")\n", "    except Exception as e:\n", "        print(f\"获取实时可转债数据时出错: {e}\")\n", "        return None\n", "    \n", "    # 步骤2：加载历史数据\n", "    print(f\"正在从{historical_csv_path}加载历史数据...\")\n", "    if os.path.exists(historical_csv_path):\n", "        historical_df = pd.read_csv(historical_csv_path)\n", "        print(f\"已加载{len(historical_df)}条历史记录\")\n", "    else:\n", "        print(f\"未找到历史文件{historical_csv_path}，将创建新文件\")\n", "        historical_df = pd.DataFrame(columns=[\n", "            'ts_code', 'trade_date', 'pre_close', 'open', 'high', 'low', 'close', \n", "            'change', 'pct_chg', 'vol', 'amount', 'bond_value', 'bond_over_rate', \n", "            'cb_value', 'cb_over_rate'\n", "        ])\n", "    \n", "    # 步骤3：准备今日数据\n", "    today_date = get_today_date_str()\n", "    new_records = []\n", "    \n", "    # 步骤4：处理每个可转债\n", "    print(\"正在处理每个可转债并获取价值分析...\")\n", "    for index, row in bond_real_time_df.iterrows():\n", "        # 跳过成交额为0的记录\n", "        if float(row['amount']) == 0:\n", "            continue\n", "            \n", "        symbol = row['symbol']\n", "        ts_code = convert_symbol_format(symbol)\n", "        \n", "        # 准备新记录\n", "        new_record = {\n", "            'ts_code': ts_code,\n", "            'trade_date': today_date,\n", "            'pre_close': None,  # 不需要拼接\n", "            'open': row['open'],\n", "            'high': row['high'],\n", "            'low': row['low'],\n", "            'close': row['trade'],\n", "            'change': None,  # 不需要拼接\n", "            'pct_chg': row['changepercent'],\n", "            'vol': row['volume'],\n", "            'amount': row['amount'],\n", "            'bond_value': None,\n", "            'bond_over_rate': None,\n", "            'cb_value': None,\n", "            'cb_over_rate': None\n", "        }\n", "        \n", "        # 获取价值分析数据\n", "        try:\n", "            analysis_symbol = convert_historical_to_akshare(ts_code)\n", "            value_analysis_df = ak.bond_zh_cov_value_analysis(symbol=analysis_symbol)\n", "            \n", "            # 确保数据不为空\n", "            if not value_analysis_df.empty:\n", "                # 获取最新日期的数据\n", "                latest_data = value_analysis_df.iloc[0]\n", "                \n", "                # 转换日期格式 (2019-01-24 -> 20190124)\n", "                date_str = latest_data['日期'].replace('-', '')\n", "                \n", "                # 更新价值分析数据\n", "                new_record['bond_value'] = latest_data['纯债价值']\n", "                new_record['bond_over_rate'] = latest_data['纯债溢价率']\n", "                new_record['cb_value'] = latest_data['转股价值']\n", "                new_record['cb_over_rate'] = latest_data['转股溢价率']\n", "                \n", "                new_records.append(new_record)\n", "                print(f\"已处理可转债: {ts_code}\")\n", "        except Exception as e:\n", "            print(f\"处理可转债{ts_code}时出错: {e}\")\n", "    \n", "    # 步骤5：创建新记录的DataFrame\n", "    new_data_df = pd.DataFrame(new_records)\n", "    \n", "    # 步骤6：检查是否已存在今日数据，避免重复\n", "    if not historical_df.empty:\n", "        historical_df = historical_df[historical_df['trade_date'] != today_date]\n", "    \n", "    # 步骤7：合并数据并保存\n", "    updated_df = pd.concat([historical_df, new_data_df], ignore_index=True)\n", "    updated_df.to_csv(historical_csv_path, index=False)\n", "    print(f\"已成功更新{len(new_records)}条新记录到{historical_csv_path}\")\n", "    \n", "    return updated_df\n", "\n", "if __name__ == \"__main__\":\n", "    # 使用示例\n", "    historical_csv_path = \"akshare_data\\\\cb_daily2.csv\"  # 替换为你的历史CSV文件路径\n", "    updated_data = fetch_and_merge_data(historical_csv_path)\n", "    if updated_data is not None:\n", "        print(\"数据更新完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "RenderPMError", "evalue": "cannot import desired renderPM backend rlPyCairo\nSeek advice at the users list see\nhttps://pairlist2.pair.net/mailman/listinfo/reportlab-users", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "File \u001b[1;32md:\\ProgramData\\miniconda3\\envs\\v8new\\lib\\site-packages\\reportlab\\graphics\\renderPM.py:47\u001b[0m, in \u001b[0;36m_getPMBackend\u001b[1;34m(backend)\u001b[0m\n\u001b[0;32m     46\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 47\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mrlPyCairo\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mM\u001b[39;00m\n\u001b[0;32m     48\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m errMsg:\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'rlPyCairo'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "File \u001b[1;32md:\\ProgramData\\miniconda3\\envs\\v8new\\lib\\site-packages\\reportlab\\graphics\\renderPM.py:50\u001b[0m, in \u001b[0;36m_getPMBackend\u001b[1;34m(backend)\u001b[0m\n\u001b[0;32m     49\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m---> 50\u001b[0m     \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01m_rl_renderPM\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mM\u001b[39;00m\n\u001b[0;32m     51\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named '_rl_renderPM'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>r\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 9\u001b[0m\n\u001b[0;32m      6\u001b[0m     renderPM\u001b[38;5;241m.\u001b[39mdrawToFile(drawing, png_path, fmt\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mPNG\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      8\u001b[0m \u001b[38;5;66;03m# 使用示例\u001b[39;00m\n\u001b[1;32m----> 9\u001b[0m \u001b[43msvg_to_png\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43msamba-model-architecture.svg\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43msamba-model-architecture.png\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[4], line 6\u001b[0m, in \u001b[0;36msvg_to_png\u001b[1;34m(svg_path, png_path)\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msvg_to_png\u001b[39m(svg_path, png_path):\n\u001b[0;32m      5\u001b[0m     drawing \u001b[38;5;241m=\u001b[39m svg2rlg(svg_path)\n\u001b[1;32m----> 6\u001b[0m     \u001b[43mrenderPM\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdrawToFile\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdrawing\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpng_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfmt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mPNG\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\ProgramData\\miniconda3\\envs\\v8new\\lib\\site-packages\\reportlab\\graphics\\renderPM.py:743\u001b[0m, in \u001b[0;36mdrawToFile\u001b[1;34m(d, fn, fmt, dpi, bg, configPIL, showBoundary, backend, backendFmt, **kwds)\u001b[0m\n\u001b[0;32m    740\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdrawToFile\u001b[39m(d,fn,fmt\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mGIF\u001b[39m\u001b[38;5;124m'\u001b[39m, dpi\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m72\u001b[39m, bg\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0xffffff\u001b[39m, configPIL\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, showBoundary\u001b[38;5;241m=\u001b[39mrl_config\u001b[38;5;241m.\u001b[39m_unset_,backend\u001b[38;5;241m=\u001b[39mrl_config\u001b[38;5;241m.\u001b[39mrenderPMBackend,backendFmt\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRGB\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds):\n\u001b[0;32m    741\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m'''create a pixmap and draw drawing, d to it then save as a file\u001b[39;00m\n\u001b[0;32m    742\u001b[0m \u001b[38;5;124;03m    configPIL dict is passed to image save method'''\u001b[39;00m\n\u001b[1;32m--> 743\u001b[0m     c \u001b[38;5;241m=\u001b[39m drawToPMCanvas(d, dpi\u001b[38;5;241m=\u001b[39mdpi, bg\u001b[38;5;241m=\u001b[39mbg, configPIL\u001b[38;5;241m=\u001b[39mconfigPIL, showBoundary\u001b[38;5;241m=\u001b[39mshowBoundary,backend\u001b[38;5;241m=\u001b[39mbackend,backendFmt\u001b[38;5;241m=\u001b[39mbackendFmt, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[0;32m    744\u001b[0m     c\u001b[38;5;241m.\u001b[39msaveToFile(fn,fmt)\n", "File \u001b[1;32md:\\ProgramData\\miniconda3\\envs\\v8new\\lib\\site-packages\\reportlab\\graphics\\renderPM.py:728\u001b[0m, in \u001b[0;36mdrawToPMCanvas\u001b[1;34m(d, dpi, bg, configPIL, showBoundary, backend, backendFmt, **kwds)\u001b[0m\n\u001b[0;32m    726\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdrawToPMCanvas\u001b[39m(d, dpi\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m72\u001b[39m, bg\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0xffffff\u001b[39m, configPIL\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, showBoundary\u001b[38;5;241m=\u001b[39mrl_config\u001b[38;5;241m.\u001b[39m_unset_,backend\u001b[38;5;241m=\u001b[39mrl_config\u001b[38;5;241m.\u001b[39mrenderPMBackend,backendFmt\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRGB\u001b[39m\u001b[38;5;124m'\u001b[39m,\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds):\n\u001b[0;32m    727\u001b[0m     d \u001b[38;5;241m=\u001b[39m renderScaledDrawing(d)\n\u001b[1;32m--> 728\u001b[0m     c \u001b[38;5;241m=\u001b[39m \u001b[43mPMCanvas\u001b[49m\u001b[43m(\u001b[49m\u001b[43md\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwidth\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43md\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheight\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdpi\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdpi\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbg\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconfigPIL\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfigPIL\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbackend\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbackend\u001b[49m\u001b[43m,\u001b[49m\u001b[43mbackendFmt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbackendFmt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    729\u001b[0m     draw(d, c, \u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m0\u001b[39m, showBoundary\u001b[38;5;241m=\u001b[39mshowBoundary,\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[0;32m    730\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m c\n", "File \u001b[1;32md:\\ProgramData\\miniconda3\\envs\\v8new\\lib\\site-packages\\reportlab\\graphics\\renderPM.py:317\u001b[0m, in \u001b[0;36mPMCanvas.__init__\u001b[1;34m(self, w, h, dpi, bg, configPIL, backend, backendFmt)\u001b[0m\n\u001b[0;32m    315\u001b[0m w \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(w\u001b[38;5;241m*\u001b[39mscale\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m0.5\u001b[39m)\n\u001b[0;32m    316\u001b[0m h \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(h\u001b[38;5;241m*\u001b[39mscale\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m0.5\u001b[39m)\n\u001b[1;32m--> 317\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__dict__\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_gs\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_getGState\u001b[49m\u001b[43m(\u001b[49m\u001b[43mw\u001b[49m\u001b[43m,\u001b[49m\u001b[43mh\u001b[49m\u001b[43m,\u001b[49m\u001b[43mbg\u001b[49m\u001b[43m,\u001b[49m\u001b[43mbackend\u001b[49m\u001b[43m,\u001b[49m\u001b[43mfmt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbackendFmt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    318\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__dict__\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_bg\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m bg\n\u001b[0;32m    319\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__dict__\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_baseCTM\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m (scale,\u001b[38;5;241m0\u001b[39m,\u001b[38;5;241m0\u001b[39m,scale,\u001b[38;5;241m0\u001b[39m,\u001b[38;5;241m0\u001b[39m)\n", "File \u001b[1;32md:\\ProgramData\\miniconda3\\envs\\v8new\\lib\\site-packages\\reportlab\\graphics\\renderPM.py:331\u001b[0m, in \u001b[0;36mPMCanvas._getGState\u001b[1;34m(w, h, bg, backend, fmt)\u001b[0m\n\u001b[0;32m    329\u001b[0m \u001b[38;5;129m@staticmethod\u001b[39m\n\u001b[0;32m    330\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_getGState\u001b[39m(w, h, bg, backend\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, fmt\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mRGB24\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m--> 331\u001b[0m     mod \u001b[38;5;241m=\u001b[39m \u001b[43m_getPMBackend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbackend\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    332\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m backend \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    333\u001b[0m         backend \u001b[38;5;241m=\u001b[39m rl_config\u001b[38;5;241m.\u001b[39mrenderPMBackend\n", "File \u001b[1;32md:\\ProgramData\\miniconda3\\envs\\v8new\\lib\\site-packages\\reportlab\\graphics\\renderPM.py:52\u001b[0m, in \u001b[0;36m_getPMBackend\u001b[1;34m(backend)\u001b[0m\n\u001b[0;32m     50\u001b[0m                 \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01m_rl_renderPM\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mM\u001b[39;00m\n\u001b[0;32m     51\u001b[0m             \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[1;32m---> 52\u001b[0m                 \u001b[38;5;28;01mraise\u001b[39;00m RenderPMError(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\u001b[38;5;124mcannot import desired renderPM backend \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mbackend\u001b[38;5;132;01m}\u001b[39;00m\n\u001b[0;32m     53\u001b[0m \u001b[38;5;124mSeek advice at the users list see\u001b[39m\n\u001b[0;32m     54\u001b[0m \u001b[38;5;124mhttps://pairlist2.pair.net/mailman/listinfo/reportlab-users\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m)\n\u001b[0;32m     55\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m     56\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m RenderPMError(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mInvalid renderPM backend, \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mbackend\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[1;31mRenderPMError\u001b[0m: cannot import desired renderPM backend rlPyCairo\nSeek advice at the users list see\nhttps://pairlist2.pair.net/mailman/listinfo/reportlab-users"]}], "source": ["from wand.image import Image\n", "\n", "def svg_to_png(svg_path, png_path):\n", "    with Image(filename=svg_path) as img:\n", "        img.format = 'png'\n", "        img.save(filename=png_path)\n", "\n", "\n", "# 使用示例\n", "svg_to_png('samba-model-architecture.svg', 'samba-model-architecture.png')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   user_id        到期时间    到期积分\n", "0   786757  2026-05-12  3000.0\n", "1   786757  2026-04-19  2000.0\n"]}], "source": ["import tushare as ts\n", "pro = ts.pro_api()\n", "\n", "#设置你的token\n", "df = pro.user(token='e5991012344cb5807859d974da3d1a08a98f5c404bf8dace4e7e4ebe')\n", "\n", "print(df)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["\n", "TUSHARE_TOKEN = 'e5991012344cb5807859d974da3d1a08a98f5c404bf8dace4e7e4ebe'\n", "import tushare as ts\n", "ts.set_token(TUSHARE_TOKEN)\n", "pro = ts.pro_api()\n", "df = pro.cyq_perf(ts_code='300773.SZ', start_date='20250601', end_date='20250620')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["df.to_csv('tushare_data\\\\cb_daily6666.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "v8new", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}}, "nbformat": 4, "nbformat_minor": 2}