# -*- coding: utf-8 -*-
"""
强化学习奖励函数实现
针对股票交易任务设计的多种奖励函数
"""

import numpy as np
import torch
from typing import Dict, List, Optional, Any
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *

class BaseRewardCalculator:
    """奖励计算器基类"""
    
    def __init__(self, risk_penalty: float = RISK_PENALTY):
        self.risk_penalty = risk_penalty
    
    def calculate_reward(self, **kwargs) -> float:
        """计算奖励"""
        raise NotImplementedError

class PortfolioRewardCalculator(BaseRewardCalculator):
    """
    投资组合奖励计算器
    基于投资组合价值变化、风险和交易成本的综合奖励
    """
    
    def __init__(self, 
                 return_weight: float = 1.0,
                 risk_weight: float = 0.1,
                 transaction_weight: float = 0.01,
                 sharpe_weight: float = 0.5):
        super().__init__()
        
        self.return_weight = return_weight
        self.risk_weight = risk_weight
        self.transaction_weight = transaction_weight
        self.sharpe_weight = sharpe_weight
        
        # 历史收益率用于计算夏普比率
        self.return_history = []
        self.max_history_length = 252  # 一年的交易日
    
    def calculate_reward(self, 
                        old_value: float,
                        new_value: float,
                        trades: List[Dict],
                        positions: Dict[str, int],
                        cash: float,
                        **kwargs) -> float:
        """
        计算综合奖励
        
        Args:
            old_value: 旧的投资组合价值
            new_value: 新的投资组合价值
            trades: 交易列表
            positions: 当前持仓
            cash: 现金
            
        Returns:
            reward: 计算的奖励值
        """
        # 1. 收益奖励
        if old_value > 0:
            portfolio_return = (new_value - old_value) / old_value
        else:
            portfolio_return = 0.0
        
        return_reward = self.return_weight * portfolio_return
        
        # 更新收益历史
        self.return_history.append(portfolio_return)
        if len(self.return_history) > self.max_history_length:
            self.return_history.pop(0)
        
        # 2. 风险惩罚
        risk_penalty = self._calculate_risk_penalty(positions, new_value)
        
        # 3. 交易成本惩罚
        transaction_penalty = self._calculate_transaction_penalty(trades)
        
        # 4. 夏普比率奖励
        sharpe_reward = self._calculate_sharpe_reward()
        
        # 综合奖励
        total_reward = (return_reward - 
                       self.risk_weight * risk_penalty - 
                       self.transaction_weight * transaction_penalty + 
                       self.sharpe_weight * sharpe_reward)
        
        return total_reward
    
    def _calculate_risk_penalty(self, positions: Dict[str, int], total_value: float) -> float:
        """计算风险惩罚"""
        if total_value <= 0:
            return 1.0  # 最大惩罚
        
        # 计算仓位集中度
        position_values = []
        for stock, shares in positions.items():
            # 这里需要股票价格，简化处理
            position_value = shares * 10  # 假设平均价格为10
            position_values.append(position_value)
        
        if not position_values:
            return 0.0
        
        position_ratios = np.array(position_values) / total_value
        
        # 使用赫芬达尔指数衡量集中度
        herfindahl_index = np.sum(position_ratios ** 2)
        
        # 集中度越高，惩罚越大
        concentration_penalty = max(0, herfindahl_index - 0.5)  # 0.5为阈值
        
        return concentration_penalty
    
    def _calculate_transaction_penalty(self, trades: List[Dict]) -> float:
        """计算交易成本惩罚"""
        if not trades:
            return 0.0
        
        total_transaction_cost = 0.0
        for trade in trades:
            if 'fee' in trade:
                total_transaction_cost += trade['fee']
        
        # 标准化交易成本
        normalized_cost = total_transaction_cost / INITIAL_CAPITAL
        
        return normalized_cost
    
    def _calculate_sharpe_reward(self) -> float:
        """计算夏普比率奖励"""
        if len(self.return_history) < 10:  # 需要足够的历史数据
            return 0.0
        
        returns = np.array(self.return_history)
        
        # 计算夏普比率
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return > 0:
            sharpe_ratio = mean_return / std_return
            # 将夏普比率转换为奖励（使用tanh函数限制范围）
            sharpe_reward = np.tanh(sharpe_ratio)
        else:
            sharpe_reward = 0.0
        
        return sharpe_reward

class MomentumRewardCalculator(BaseRewardCalculator):
    """
    动量奖励计算器
    专门针对捕获早期上涨趋势的奖励函数
    """
    
    def __init__(self, 
                 momentum_weight: float = 1.0,
                 trend_weight: float = 0.5,
                 timing_weight: float = 0.3):
        super().__init__()
        
        self.momentum_weight = momentum_weight
        self.trend_weight = trend_weight
        self.timing_weight = timing_weight
        
        # 价格历史用于趋势分析
        self.price_history = {}
        self.max_history_length = 20
    
    def calculate_reward(self, 
                        stock_prices: Dict[str, float],
                        positions: Dict[str, int],
                        trades: List[Dict],
                        **kwargs) -> float:
        """
        计算动量奖励
        
        Args:
            stock_prices: 当前股票价格
            positions: 当前持仓
            trades: 交易列表
            
        Returns:
            reward: 动量奖励
        """
        total_reward = 0.0
        
        # 更新价格历史
        for stock, price in stock_prices.items():
            if stock not in self.price_history:
                self.price_history[stock] = []
            
            self.price_history[stock].append(price)
            if len(self.price_history[stock]) > self.max_history_length:
                self.price_history[stock].pop(0)
        
        # 计算各股票的动量奖励
        for stock, shares in positions.items():
            if shares > 0 and stock in self.price_history:
                momentum_reward = self._calculate_stock_momentum_reward(stock)
                total_reward += shares * momentum_reward
        
        # 计算交易时机奖励
        timing_reward = self._calculate_timing_reward(trades, stock_prices)
        total_reward += self.timing_weight * timing_reward
        
        return total_reward
    
    def _calculate_stock_momentum_reward(self, stock: str) -> float:
        """计算单只股票的动量奖励"""
        prices = self.price_history[stock]
        
        if len(prices) < 5:
            return 0.0
        
        # 计算短期和长期趋势
        short_term = np.mean(prices[-3:])  # 最近3天
        medium_term = np.mean(prices[-7:])  # 最近7天
        long_term = np.mean(prices[-14:]) if len(prices) >= 14 else np.mean(prices)
        
        # 动量指标
        short_momentum = (short_term - medium_term) / medium_term if medium_term > 0 else 0
        long_momentum = (medium_term - long_term) / long_term if long_term > 0 else 0
        
        # 趋势强度
        trend_strength = self._calculate_trend_strength(prices)
        
        # 综合动量奖励
        momentum_reward = (self.momentum_weight * short_momentum + 
                          self.trend_weight * long_momentum) * trend_strength
        
        return momentum_reward
    
    def _calculate_trend_strength(self, prices: List[float]) -> float:
        """计算趋势强度"""
        if len(prices) < 3:
            return 0.0
        
        # 计算价格变化的一致性
        changes = np.diff(prices)
        
        # 上涨天数比例
        up_days = np.sum(changes > 0) / len(changes)
        
        # 变化幅度的标准差（越小表示趋势越稳定）
        change_std = np.std(changes)
        stability = 1.0 / (1.0 + change_std)
        
        # 综合趋势强度
        trend_strength = up_days * stability
        
        return trend_strength
    
    def _calculate_timing_reward(self, trades: List[Dict], stock_prices: Dict[str, float]) -> float:
        """计算交易时机奖励"""
        timing_reward = 0.0
        
        for trade in trades:
            stock = trade.get('stock')
            action = trade.get('action')
            
            if stock in self.price_history and len(self.price_history[stock]) >= 3:
                recent_trend = self._get_recent_trend(stock)
                
                # 奖励在上涨趋势开始时买入
                if action == 'buy' and recent_trend > 0:
                    timing_reward += recent_trend
                
                # 奖励在下跌趋势开始时卖出
                elif action == 'sell' and recent_trend < 0:
                    timing_reward += abs(recent_trend)
        
        return timing_reward
    
    def _get_recent_trend(self, stock: str) -> float:
        """获取最近的趋势方向"""
        prices = self.price_history[stock]
        
        if len(prices) < 3:
            return 0.0
        
        # 计算最近3天的趋势
        recent_change = (prices[-1] - prices[-3]) / prices[-3]
        
        return recent_change

class RiskAdjustedRewardCalculator(BaseRewardCalculator):
    """
    风险调整奖励计算器
    考虑风险调整后的收益
    """
    
    def __init__(self, 
                 var_alpha: float = 0.05,
                 lookback_days: int = 20):
        super().__init__()
        
        self.var_alpha = var_alpha  # VaR置信水平
        self.lookback_days = lookback_days
        
        # 收益历史
        self.return_history = []
    
    def calculate_reward(self, 
                        portfolio_return: float,
                        **kwargs) -> float:
        """
        计算风险调整奖励
        
        Args:
            portfolio_return: 投资组合收益率
            
        Returns:
            risk_adjusted_reward: 风险调整后的奖励
        """
        # 更新收益历史
        self.return_history.append(portfolio_return)
        if len(self.return_history) > self.lookback_days:
            self.return_history.pop(0)
        
        if len(self.return_history) < 5:
            return portfolio_return
        
        # 计算VaR
        var = self._calculate_var()
        
        # 计算条件VaR (CVaR)
        cvar = self._calculate_cvar()
        
        # 风险调整奖励
        risk_adjustment = max(0, portfolio_return + var)  # 如果收益超过VaR损失，给予奖励
        
        # 如果收益为负且超过CVaR，给予额外惩罚
        if portfolio_return < 0 and portfolio_return < cvar:
            risk_adjustment -= abs(portfolio_return - cvar)
        
        return risk_adjustment
    
    def _calculate_var(self) -> float:
        """计算Value at Risk"""
        returns = np.array(self.return_history)
        var = np.percentile(returns, self.var_alpha * 100)
        return var
    
    def _calculate_cvar(self) -> float:
        """计算Conditional Value at Risk"""
        returns = np.array(self.return_history)
        var = self._calculate_var()
        
        # CVaR是超过VaR的损失的期望值
        tail_losses = returns[returns <= var]
        
        if len(tail_losses) > 0:
            cvar = np.mean(tail_losses)
        else:
            cvar = var
        
        return cvar

class MultiObjectiveRewardCalculator(BaseRewardCalculator):
    """
    多目标奖励计算器
    综合多种奖励函数
    """
    
    def __init__(self):
        super().__init__()
        
        self.portfolio_calculator = PortfolioRewardCalculator()
        self.momentum_calculator = MomentumRewardCalculator()
        self.risk_calculator = RiskAdjustedRewardCalculator()
        
        # 权重
        self.weights = {
            'portfolio': 0.5,
            'momentum': 0.3,
            'risk': 0.2
        }
    
    def calculate_reward(self, **kwargs) -> float:
        """计算多目标综合奖励"""
        # 投资组合奖励
        portfolio_reward = self.portfolio_calculator.calculate_reward(**kwargs)
        
        # 动量奖励
        momentum_reward = self.momentum_calculator.calculate_reward(**kwargs)
        
        # 风险调整奖励
        portfolio_return = kwargs.get('portfolio_return', 0.0)
        risk_reward = self.risk_calculator.calculate_reward(
            portfolio_return=portfolio_return
        )
        
        # 加权综合
        total_reward = (self.weights['portfolio'] * portfolio_reward +
                       self.weights['momentum'] * momentum_reward +
                       self.weights['risk'] * risk_reward)
        
        return total_reward

if __name__ == "__main__":
    # 测试代码
    print("测试奖励函数...")
    
    # 测试投资组合奖励计算器
    portfolio_calc = PortfolioRewardCalculator()
    
    reward = portfolio_calc.calculate_reward(
        old_value=100000,
        new_value=101000,
        trades=[{'fee': 10}],
        positions={'stock1': 100},
        cash=50000
    )
    
    print(f"投资组合奖励: {reward:.6f}")
    
    # 测试动量奖励计算器
    momentum_calc = MomentumRewardCalculator()
    
    momentum_reward = momentum_calc.calculate_reward(
        stock_prices={'stock1': 10.5},
        positions={'stock1': 100},
        trades=[{'stock': 'stock1', 'action': 'buy'}]
    )
    
    print(f"动量奖励: {momentum_reward:.6f}")
    
    print("✓ 奖励函数测试通过")
