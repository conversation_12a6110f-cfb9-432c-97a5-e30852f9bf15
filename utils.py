import os
import random
import numpy as np
import torch
import matplotlib.pyplot as plt
import pandas as pd
from tqdm import tqdm
from sklearn.metrics import mean_squared_error, r2_score
import config
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体（Windows通常自带）
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
def setup_seed(seed):
    """
    设置随机种子以保证实验可重复性
    
    参数:
        seed: 种子数
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    print(f"已设置随机种子: {seed}")

def plot_training_curves(train_losses, val_losses, val_metrics_history, save_path=None):
    """
    绘制训练曲线
    
    参数:
        train_losses: 训练损失列表
        val_losses: 验证损失列表
        val_metrics_history: 验证指标历史
        save_path: 图像保存路径
    """
    epochs = range(1, len(train_losses) + 1)
    
    # 创建一个2x2的子图
    fig, axs = plt.subplots(2, 2, figsize=(15, 10))
    
    # 损失曲线
    axs[0, 0].plot(epochs, train_losses, 'b-', label='Train Loss')
    axs[0, 0].plot(epochs, val_losses, 'r-', label='Val Loss')
    axs[0, 0].set_title('Loss Curve')
    axs[0, 0].set_xlabel('Eopch')
    axs[0, 0].set_ylabel('Loss')
    axs[0, 0].legend()
    axs[0, 0].grid(True)
    
    # RMSE曲线
    rmse_values = [m['rmse'] for m in val_metrics_history]
    axs[0, 1].plot(epochs, rmse_values, 'g-')
    axs[0, 1].set_title('RMSE Curve')
    axs[0, 1].set_xlabel('Eopch')
    axs[0, 1].set_ylabel('RMSE')
    axs[0, 1].grid(True)
    
    # IC曲线
    ic_values = [m['ic'] for m in val_metrics_history]
    axs[1, 0].plot(epochs, ic_values, 'c-')
    axs[1, 0].set_title('IC Curve')
    axs[1, 0].set_xlabel('Eopch')
    axs[1, 0].set_ylabel('IC')
    axs[1, 0].grid(True)
    
    # RIC曲线
    ric_values = [m['ric'] for m in val_metrics_history]
    axs[1, 1].plot(epochs, ric_values, 'm-')
    axs[1, 1].set_title('RIC Curve')
    axs[1, 1].set_xlabel('Eopch')
    axs[1, 1].set_ylabel('RIC')
    axs[1, 1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        print(f"已保存训练曲线到 {save_path}")
    
    plt.close()

def predict_dataloader(model, dataloader, device):
    """
    对数据加载器中的数据进行预测
    
    参数:
        model: 模型
        dataloader: 数据加载器
        device: 设备
        
    返回:
        predictions: 预测结果
        targets: 实际值
    """
    model.eval()
    predictions = []
    targets = []
    
    with torch.no_grad():
        for features, target in dataloader:
            features = features.to(device)
            outputs = model(features)
            predictions.extend(outputs.cpu().numpy())
            targets.extend(target.numpy())
    
    return np.array(predictions), np.array(targets)



def validate(model, val_loader, criterion, device):
    """
    在验证集上评估模型
    
    参数:
        model: 模型
        val_loader: 验证数据加载器
        criterion: 损失函数
        device: 设备
        
    返回:
        avg_loss: 平均损失
        metrics: 评估指标字典
    """
    model.eval()
    total_loss = 0
    all_targets = []
    all_preds = []
    
    with torch.no_grad():
        for features, targets in tqdm(val_loader, desc="验证中"):
            features, targets = features.to(device), targets.to(device)
            
            # 前向传播
            outputs = model(features)
            loss = criterion(outputs, targets)
            
            total_loss += loss.item() * features.size(0)
            all_targets.extend(targets.cpu().numpy())
            all_preds.extend(outputs.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader.dataset)
    
    # 计算评估指标
    all_targets = np.array(all_targets)
    all_preds = np.array(all_preds)
    rmse = np.sqrt(mean_squared_error(all_targets, all_preds))
    r2 = r2_score(all_targets, all_preds)
    
    # 计算IC和RIC (按论文指标)
    ic = np.corrcoef(all_preds, all_targets)[0, 1]
    
    # 计算RIC (Rank IC)
    ranks_pred = pd.Series(all_preds).rank()
    ranks_target = pd.Series(all_targets).rank()
    ric = ranks_pred.corr(ranks_target, method='spearman')
    
    metrics = {
        'rmse': rmse,
        'r2': r2,
        'ic': ic,
        'ric': ric
    }
    
    return avg_loss, metrics
