# -*- coding: utf-8 -*-
"""
股票数据加载器
用于加载和预处理股票数据
"""

import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Tuple
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *

class StockDataLoader:
    """
    股票数据加载器
    支持多种数据格式和预处理功能
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """
        加载股票数据
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            加载的数据DataFrame
        """
        try:
            # 根据文件扩展名选择加载方法
            if file_path.endswith('.csv'):
                data = pd.read_csv(file_path, encoding='utf-8')
            elif file_path.endswith('.parquet'):
                data = pd.read_parquet(file_path)
            elif file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                data = pd.read_excel(file_path)
            else:
                # 默认尝试CSV格式
                data = pd.read_csv(file_path, encoding='utf-8')
            
            self.logger.info(f"成功加载数据: {len(data)} 条记录")
            self.logger.info(f"数据列: {list(data.columns)}")
            
            return data
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {str(e)}")
            raise
    
    def preprocess_data(self, 
                       data: pd.DataFrame,
                       features: List[str] = TECHNICAL_FACTORS,
                       target: str = TARGET_VARIABLE) -> pd.DataFrame:
        """
        预处理数据
        
        Args:
            data: 原始数据
            features: 特征列表
            target: 目标变量
            
        Returns:
            预处理后的数据
        """
        processed_data = data.copy()
        
        # 1. 日期处理
        processed_data = self._process_dates(processed_data)
        
        # 2. 数据清洗
        processed_data = self._clean_data(processed_data)
        
        # 3. 特征工程
        processed_data = self._engineer_features(processed_data, features)
        
        # 4. 目标变量处理
        if target in processed_data.columns:
            processed_data = self._process_target(processed_data, target)
        
        # 5. 数据标准化
        processed_data = self._normalize_features(processed_data, features)
        
        # 6. 移除无效数据
        processed_data = self._remove_invalid_data(processed_data)
        
        self.logger.info(f"数据预处理完成: {len(processed_data)} 条记录")
        
        return processed_data
    
    def _process_dates(self, data: pd.DataFrame) -> pd.DataFrame:
        """处理日期列"""
        # 尝试不同的日期列名
        date_columns = ['trade_date', 'date', 'trading_date', 'dt']
        
        for col in date_columns:
            if col in data.columns:
                try:
                    data[col] = pd.to_datetime(data[col])
                    data = data.sort_values(col)
                    self.logger.info(f"日期列 '{col}' 处理完成")
                    break
                except Exception as e:
                    self.logger.warning(f"日期列 '{col}' 处理失败: {str(e)}")
        
        return data
    
    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据清洗"""
        # 移除重复行
        before_count = len(data)
        data = data.drop_duplicates()
        after_count = len(data)
        
        if before_count != after_count:
            self.logger.info(f"移除重复行: {before_count - after_count} 条")
        
        # 处理缺失值
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            if data[col].isnull().sum() > 0:
                # 使用前向填充和后向填充
                data[col] = data[col].fillna(method='ffill').fillna(method='bfill')
                
                # 如果仍有缺失值，使用均值填充
                if data[col].isnull().sum() > 0:
                    data[col] = data[col].fillna(data[col].mean())
        
        return data
    
    def _engineer_features(self, data: pd.DataFrame, features: List[str]) -> pd.DataFrame:
        """特征工程"""
        # 确保基础价格数据存在
        required_columns = ['close', 'open', 'high', 'low', 'vol']
        
        for col in required_columns:
            if col not in data.columns:
                # 如果缺少基础数据，创建模拟数据
                if col == 'close':
                    data[col] = 10.0  # 默认收盘价
                elif col == 'open':
                    data[col] = data.get('close', 10.0)
                elif col == 'high':
                    data[col] = data.get('close', 10.0) * 1.02
                elif col == 'low':
                    data[col] = data.get('close', 10.0) * 0.98
                elif col == 'vol':
                    data[col] = 1000000  # 默认成交量
        
        # 计算缺失的技术指标
        for feature in features:
            if feature not in data.columns:
                data[feature] = self._calculate_technical_indicator(data, feature)
        
        return data
    
    def _calculate_technical_indicator(self, data: pd.DataFrame, indicator: str) -> pd.Series:
        """计算技术指标"""
        try:
            if indicator == 'pct_chg':
                return data['close'].pct_change() * 100
            elif indicator == 'ma5':
                return data['close'].rolling(window=5).mean()
            elif indicator == 'ma10':
                return data['close'].rolling(window=10).mean()
            elif indicator == 'ma20':
                return data['close'].rolling(window=20).mean()
            elif indicator == 'ma60':
                return data['close'].rolling(window=60).mean()
            elif indicator == 'rsi':
                return self._calculate_rsi(data['close'])
            elif indicator == 'macd':
                return self._calculate_macd(data['close'])
            elif indicator == 'boll_upper' or indicator == 'boll_mid' or indicator == 'boll_lower':
                return self._calculate_bollinger_bands(data['close'], indicator)
            else:
                # 对于未知指标，返回随机数据
                return pd.Series(np.random.randn(len(data)), index=data.index)
                
        except Exception as e:
            self.logger.warning(f"计算技术指标 '{indicator}' 失败: {str(e)}")
            return pd.Series(np.random.randn(len(data)), index=data.index)
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26) -> pd.Series:
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        return macd
    
    def _calculate_bollinger_bands(self, prices: pd.Series, band_type: str, window: int = 20) -> pd.Series:
        """计算布林带指标"""
        ma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        
        if band_type == 'boll_upper':
            return ma + (std * 2)
        elif band_type == 'boll_lower':
            return ma - (std * 2)
        else:  # boll_mid
            return ma
    
    def _process_target(self, data: pd.DataFrame, target: str) -> pd.DataFrame:
        """处理目标变量"""
        if target not in data.columns:
            # 如果目标变量不存在，根据配置创建
            if target == TARGET_VARIABLE:
                # 计算目标收益率: (day 3 high price / day 2 open price - 1) * 100
                data[target] = self._calculate_target_return(data)
        
        return data
    
    def _calculate_target_return(self, data: pd.DataFrame) -> pd.Series:
        """计算目标收益率"""
        try:
            # 按股票分组计算
            if 'ts_code' in data.columns:
                target_returns = []
                for stock_code in data['ts_code'].unique():
                    stock_data = data[data['ts_code'] == stock_code].copy()
                    stock_data = stock_data.sort_values('trade_date')
                    
                    # 计算目标收益率
                    day3_high = stock_data['high'].shift(-2)  # 第3天的最高价
                    day2_open = stock_data['open'].shift(-1)  # 第2天的开盘价
                    
                    stock_target = ((day3_high / day2_open) - 1) * 100
                    target_returns.append(stock_target)
                
                return pd.concat(target_returns)
            else:
                # 单只股票
                day3_high = data['high'].shift(-2)
                day2_open = data['open'].shift(-1)
                return ((day3_high / day2_open) - 1) * 100
                
        except Exception as e:
            self.logger.warning(f"计算目标收益率失败: {str(e)}")
            return pd.Series(np.random.randn(len(data)) * 5, index=data.index)
    
    def _normalize_features(self, data: pd.DataFrame, features: List[str]) -> pd.DataFrame:
        """标准化特征"""
        for feature in features:
            if feature in data.columns:
                # 使用Z-score标准化
                mean_val = data[feature].mean()
                std_val = data[feature].std()
                
                if std_val > 0:
                    data[feature] = (data[feature] - mean_val) / std_val
                else:
                    data[feature] = 0
        
        return data
    
    def _remove_invalid_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """移除无效数据"""
        # 移除包含无穷大值的行
        data = data.replace([np.inf, -np.inf], np.nan)
        
        # 移除包含NaN的行
        before_count = len(data)
        data = data.dropna()
        after_count = len(data)
        
        if before_count != after_count:
            self.logger.info(f"移除无效数据: {before_count - after_count} 条")
        
        return data
    
    def filter_by_date(self, 
                      data: pd.DataFrame,
                      start_date: str,
                      end_date: str) -> pd.DataFrame:
        """按日期过滤数据"""
        date_column = None
        
        # 查找日期列
        for col in ['trade_date', 'date', 'trading_date', 'dt']:
            if col in data.columns:
                date_column = col
                break
        
        if date_column is None:
            self.logger.warning("未找到日期列，返回原始数据")
            return data
        
        # 确保日期列是datetime类型
        data[date_column] = pd.to_datetime(data[date_column])
        
        # 过滤数据
        mask = (data[date_column] >= start_date) & (data[date_column] <= end_date)
        filtered_data = data[mask].copy()
        
        self.logger.info(f"日期过滤完成: {start_date} 到 {end_date}, {len(filtered_data)} 条记录")
        
        return filtered_data
    
    def get_stock_list(self, data: pd.DataFrame) -> List[str]:
        """获取股票代码列表"""
        if 'ts_code' in data.columns:
            return data['ts_code'].unique().tolist()
        else:
            return ['default_stock']
    
    def split_by_stock(self, data: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """按股票分割数据"""
        stock_data = {}
        
        if 'ts_code' in data.columns:
            for stock_code in data['ts_code'].unique():
                stock_df = data[data['ts_code'] == stock_code].copy()
                stock_df = stock_df.sort_values('trade_date')
                stock_data[stock_code] = stock_df
        else:
            stock_data['default_stock'] = data.copy()
        
        return stock_data

if __name__ == "__main__":
    # 测试代码
    print("测试数据加载器...")
    
    loader = StockDataLoader()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'ts_code': ['000001.SZ'] * 100,
        'trade_date': pd.date_range('2024-01-01', periods=100),
        'close': np.random.randn(100).cumsum() + 10,
        'open': np.random.randn(100).cumsum() + 10,
        'high': np.random.randn(100).cumsum() + 11,
        'low': np.random.randn(100).cumsum() + 9,
        'vol': np.random.randint(1000, 10000, 100)
    })
    
    # 测试预处理
    processed_data = loader.preprocess_data(test_data)
    print(f"预处理后数据形状: {processed_data.shape}")
    print(f"特征列: {[col for col in processed_data.columns if col in TECHNICAL_FACTORS]}")
    
    # 测试日期过滤
    filtered_data = loader.filter_by_date(processed_data, '2024-02-01', '2024-03-01')
    print(f"日期过滤后数据形状: {filtered_data.shape}")
    
    print("✓ 数据加载器测试通过")
