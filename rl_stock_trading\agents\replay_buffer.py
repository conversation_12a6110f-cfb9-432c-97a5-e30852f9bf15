# -*- coding: utf-8 -*-
"""
经验回放缓冲区实现
支持优先经验回放(Prioritized Experience Replay)
"""

import numpy as np
import torch
import random
from collections import namedtuple, deque
from typing import List, Tuple, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *

# 经验元组定义
Experience = namedtuple('Experience', 
                       ['state', 'action', 'reward', 'next_state', 'done'])

class SumTree:
    """
    用于优先经验回放的求和树数据结构
    """
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.tree = np.zeros(2 * capacity - 1)
        self.data = np.zeros(capacity, dtype=object)
        self.write = 0
        self.n_entries = 0

    def _propagate(self, idx: int, change: float):
        """向上传播优先级变化"""
        parent = (idx - 1) // 2
        self.tree[parent] += change
        if parent != 0:
            self._propagate(parent, change)

    def _retrieve(self, idx: int, s: float):
        """检索叶子节点"""
        left = 2 * idx + 1
        right = left + 1

        if left >= len(self.tree):
            return idx

        if s <= self.tree[left]:
            return self._retrieve(left, s)
        else:
            return self._retrieve(right, s - self.tree[left])

    def total(self) -> float:
        """返回总优先级"""
        return self.tree[0]

    def add(self, priority: float, data):
        """添加新经验"""
        idx = self.write + self.capacity - 1
        self.data[self.write] = data
        self.update(idx, priority)

        self.write += 1
        if self.write >= self.capacity:
            self.write = 0

        if self.n_entries < self.capacity:
            self.n_entries += 1

    def update(self, idx: int, priority: float):
        """更新优先级"""
        change = priority - self.tree[idx]
        self.tree[idx] = priority
        self._propagate(idx, change)

    def get(self, s: float):
        """根据优先级采样"""
        idx = self._retrieve(0, s)
        data_idx = idx - self.capacity + 1
        return idx, self.tree[idx], self.data[data_idx]

class PrioritizedReplayBuffer:
    """
    优先经验回放缓冲区
    """
    def __init__(self, 
                 capacity: int = REPLAY_BUFFER_SIZE,
                 alpha: float = PRIORITY_ALPHA,
                 beta_start: float = PRIORITY_BETA_START,
                 beta_frames: int = PRIORITY_BETA_FRAMES):
        """
        初始化优先经验回放缓冲区
        
        Args:
            capacity: 缓冲区容量
            alpha: 优先级指数
            beta_start: 重要性采样起始值
            beta_frames: beta增长的帧数
        """
        self.tree = SumTree(capacity)
        self.capacity = capacity
        self.alpha = alpha
        self.beta_start = beta_start
        self.beta_frames = beta_frames
        self.frame = 1
        self.epsilon = 1e-6  # 防止优先级为0

    def _get_priority(self, error: float) -> float:
        """计算优先级"""
        return (np.abs(error) + self.epsilon) ** self.alpha

    def add(self, error: float, sample: Experience):
        """添加经验"""
        priority = self._get_priority(error)
        self.tree.add(priority, sample)

    def sample(self, batch_size: int) -> Tuple[List[Experience], np.ndarray, np.ndarray]:
        """
        采样经验批次
        
        Returns:
            batch: 经验批次
            indices: 采样索引
            weights: 重要性采样权重
        """
        batch = []
        indices = []
        weights = []
        
        # 计算当前beta值
        beta = min(1.0, self.beta_start + (1.0 - self.beta_start) * self.frame / self.beta_frames)
        
        # 计算采样段
        segment = self.tree.total() / batch_size
        
        for i in range(batch_size):
            a = segment * i
            b = segment * (i + 1)
            s = random.uniform(a, b)
            
            idx, priority, data = self.tree.get(s)
            
            # 计算重要性采样权重
            sampling_prob = priority / self.tree.total()
            weight = (self.tree.n_entries * sampling_prob) ** (-beta)
            
            batch.append(data)
            indices.append(idx)
            weights.append(weight)
        
        # 归一化权重
        weights = np.array(weights)
        weights /= weights.max()
        
        self.frame += 1
        
        return batch, np.array(indices), weights

    def update(self, indices: np.ndarray, errors: np.ndarray):
        """更新优先级"""
        for idx, error in zip(indices, errors):
            priority = self._get_priority(error)
            self.tree.update(idx, priority)

    def __len__(self) -> int:
        """返回缓冲区大小"""
        return self.tree.n_entries

class SimpleReplayBuffer:
    """
    简单的经验回放缓冲区（用于对比）
    """
    def __init__(self, capacity: int = REPLAY_BUFFER_SIZE):
        self.buffer = deque(maxlen=capacity)
        self.capacity = capacity

    def add(self, experience: Experience):
        """添加经验"""
        self.buffer.append(experience)

    def sample(self, batch_size: int) -> List[Experience]:
        """随机采样"""
        return random.sample(self.buffer, batch_size)

    def __len__(self) -> int:
        return len(self.buffer)

def collate_experiences(experiences: List[Experience]) -> Tuple[torch.Tensor, ...]:
    """
    将经验列表转换为张量批次
    
    Args:
        experiences: 经验列表
        
    Returns:
        states, actions, rewards, next_states, dones 张量
    """
    states = torch.FloatTensor([e.state for e in experiences]).to(DEVICE)
    actions = torch.LongTensor([e.action for e in experiences]).to(DEVICE)
    rewards = torch.FloatTensor([e.reward for e in experiences]).to(DEVICE)
    next_states = torch.FloatTensor([e.next_state for e in experiences]).to(DEVICE)
    dones = torch.BoolTensor([e.done for e in experiences]).to(DEVICE)
    
    return states, actions, rewards, next_states, dones

if __name__ == "__main__":
    # 测试代码
    print("测试优先经验回放缓冲区...")
    
    buffer = PrioritizedReplayBuffer(capacity=1000)
    
    # 添加一些测试经验
    for i in range(100):
        state = np.random.randn(STATE_DIM)
        action = np.random.randint(ACTION_SPACE_SIZE)
        reward = np.random.randn()
        next_state = np.random.randn(STATE_DIM)
        done = np.random.choice([True, False])
        
        experience = Experience(state, action, reward, next_state, done)
        error = np.random.rand()  # 模拟TD误差
        
        buffer.add(error, experience)
    
    # 测试采样
    batch, indices, weights = buffer.sample(32)
    print(f"采样批次大小: {len(batch)}")
    print(f"权重形状: {weights.shape}")
    print(f"缓冲区大小: {len(buffer)}")
    
    print("✓ 经验回放缓冲区测试通过")
