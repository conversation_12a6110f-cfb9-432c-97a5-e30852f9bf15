# 深度强化学习股票预测系统依赖包

# 深度学习框架
torch>=1.12.0
torchvision>=0.13.0
tensorboardX>=2.5

# 数据处理
numpy>=1.21.0
pandas>=1.4.0
scikit-learn>=1.1.0

# 强化学习
gym>=0.21.0
stable-baselines3>=1.6.0

# 数学和统计
scipy>=1.8.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 进度条
tqdm>=4.64.0

# 日志和配置
pyyaml>=6.0

# 金融数据处理
tushare>=1.2.89

# 其他工具
joblib>=1.1.0
psutil>=5.9.0

# 可选依赖 (用于更好的性能)
# numba>=0.56.0  # 数值计算加速
# cupy-cuda11x>=11.0.0  # GPU加速 (根据CUDA版本选择)

# 开发和测试依赖
pytest>=7.0.0
pytest-cov>=3.0.0
black>=22.0.0
flake8>=4.0.0
