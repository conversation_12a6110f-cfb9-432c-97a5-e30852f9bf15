# -*- coding: utf-8 -*-
"""
评估指标和工具函数
"""

import numpy as np
import pandas as pd
import logging
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.config import *

def setup_logging(level: str = LOG_LEVEL):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=LOG_FORMAT,
        handlers=[
            logging.StreamHandler()
        ]
    )

def calculate_ic(predictions: np.ndarray, targets: np.ndarray) -> float:
    """
    计算信息系数(IC)
    
    Args:
        predictions: 预测值
        targets: 真实值
        
    Returns:
        IC值
    """
    if len(predictions) != len(targets) or len(predictions) == 0:
        return 0.0
    
    # 移除NaN值
    mask = ~(np.isnan(predictions) | np.isnan(targets))
    if np.sum(mask) < 2:
        return 0.0
    
    pred_clean = predictions[mask]
    target_clean = targets[mask]
    
    # 计算相关系数
    correlation = np.corrcoef(pred_clean, target_clean)[0, 1]
    
    return correlation if not np.isnan(correlation) else 0.0

def calculate_ric(predictions: np.ndarray, targets: np.ndarray) -> float:
    """
    计算排序信息系数(RIC)
    
    Args:
        predictions: 预测值
        targets: 真实值
        
    Returns:
        RIC值
    """
    if len(predictions) != len(targets) or len(predictions) == 0:
        return 0.0
    
    # 移除NaN值
    mask = ~(np.isnan(predictions) | np.isnan(targets))
    if np.sum(mask) < 2:
        return 0.0
    
    pred_clean = predictions[mask]
    target_clean = targets[mask]
    
    # 计算排序相关系数
    from scipy.stats import spearmanr
    correlation, _ = spearmanr(pred_clean, target_clean)
    
    return correlation if not np.isnan(correlation) else 0.0

def calculate_sharpe_ratio(returns: np.ndarray, risk_free_rate: float = RISK_FREE_RATE) -> float:
    """
    计算夏普比率
    
    Args:
        returns: 收益率序列
        risk_free_rate: 无风险利率
        
    Returns:
        夏普比率
    """
    if len(returns) == 0:
        return 0.0
    
    excess_returns = returns - risk_free_rate / 252  # 日化无风险利率
    
    if np.std(excess_returns) == 0:
        return 0.0
    
    return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)

def calculate_max_drawdown(values: np.ndarray) -> float:
    """
    计算最大回撤
    
    Args:
        values: 价值序列
        
    Returns:
        最大回撤
    """
    if len(values) < 2:
        return 0.0
    
    peak = np.maximum.accumulate(values)
    drawdown = (peak - values) / peak
    
    return np.max(drawdown)

def calculate_calmar_ratio(returns: np.ndarray, values: np.ndarray) -> float:
    """
    计算卡尔玛比率
    
    Args:
        returns: 收益率序列
        values: 价值序列
        
    Returns:
        卡尔玛比率
    """
    if len(returns) == 0 or len(values) == 0:
        return 0.0
    
    annual_return = np.mean(returns) * 252
    max_dd = calculate_max_drawdown(values)
    
    if max_dd == 0:
        return 0.0
    
    return annual_return / max_dd

def calculate_win_rate(returns: np.ndarray) -> float:
    """
    计算胜率
    
    Args:
        returns: 收益率序列
        
    Returns:
        胜率
    """
    if len(returns) == 0:
        return 0.0
    
    return np.sum(returns > 0) / len(returns)

def calculate_profit_loss_ratio(returns: np.ndarray) -> float:
    """
    计算盈亏比
    
    Args:
        returns: 收益率序列
        
    Returns:
        盈亏比
    """
    if len(returns) == 0:
        return 0.0
    
    profits = returns[returns > 0]
    losses = returns[returns < 0]
    
    if len(profits) == 0 or len(losses) == 0:
        return 0.0
    
    avg_profit = np.mean(profits)
    avg_loss = np.mean(np.abs(losses))
    
    return avg_profit / avg_loss

def calculate_var(returns: np.ndarray, alpha: float = 0.05) -> float:
    """
    计算风险价值(VaR)
    
    Args:
        returns: 收益率序列
        alpha: 置信水平
        
    Returns:
        VaR值
    """
    if len(returns) == 0:
        return 0.0
    
    return np.percentile(returns, alpha * 100)

def calculate_cvar(returns: np.ndarray, alpha: float = 0.05) -> float:
    """
    计算条件风险价值(CVaR)
    
    Args:
        returns: 收益率序列
        alpha: 置信水平
        
    Returns:
        CVaR值
    """
    if len(returns) == 0:
        return 0.0
    
    var = calculate_var(returns, alpha)
    tail_losses = returns[returns <= var]
    
    if len(tail_losses) == 0:
        return var
    
    return np.mean(tail_losses)

def calculate_information_ratio(portfolio_returns: np.ndarray, 
                              benchmark_returns: np.ndarray) -> float:
    """
    计算信息比率
    
    Args:
        portfolio_returns: 投资组合收益率
        benchmark_returns: 基准收益率
        
    Returns:
        信息比率
    """
    if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) == 0:
        return 0.0
    
    excess_returns = portfolio_returns - benchmark_returns
    tracking_error = np.std(excess_returns)
    
    if tracking_error == 0:
        return 0.0
    
    return np.mean(excess_returns) / tracking_error * np.sqrt(252)

def calculate_beta(portfolio_returns: np.ndarray, 
                  benchmark_returns: np.ndarray) -> float:
    """
    计算贝塔系数
    
    Args:
        portfolio_returns: 投资组合收益率
        benchmark_returns: 基准收益率
        
    Returns:
        贝塔系数
    """
    if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) == 0:
        return 0.0
    
    covariance = np.cov(portfolio_returns, benchmark_returns)[0, 1]
    benchmark_variance = np.var(benchmark_returns)
    
    if benchmark_variance == 0:
        return 0.0
    
    return covariance / benchmark_variance

def calculate_alpha(portfolio_returns: np.ndarray, 
                   benchmark_returns: np.ndarray,
                   risk_free_rate: float = RISK_FREE_RATE) -> float:
    """
    计算阿尔法系数
    
    Args:
        portfolio_returns: 投资组合收益率
        benchmark_returns: 基准收益率
        risk_free_rate: 无风险利率
        
    Returns:
        阿尔法系数
    """
    if len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) == 0:
        return 0.0
    
    beta = calculate_beta(portfolio_returns, benchmark_returns)
    
    portfolio_excess = np.mean(portfolio_returns) - risk_free_rate / 252
    benchmark_excess = np.mean(benchmark_returns) - risk_free_rate / 252
    
    alpha = portfolio_excess - beta * benchmark_excess
    
    return alpha * 252  # 年化

class MetricsCalculator:
    """指标计算器"""
    
    def __init__(self, risk_free_rate: float = RISK_FREE_RATE):
        self.risk_free_rate = risk_free_rate
    
    def calculate_all_metrics(self, 
                            portfolio_values: np.ndarray,
                            benchmark_values: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        计算所有指标
        
        Args:
            portfolio_values: 投资组合价值序列
            benchmark_values: 基准价值序列
            
        Returns:
            指标字典
        """
        if len(portfolio_values) < 2:
            return {}
        
        # 计算收益率
        portfolio_returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        metrics = {
            'total_return': (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0],
            'annualized_return': np.mean(portfolio_returns) * 252,
            'volatility': np.std(portfolio_returns) * np.sqrt(252),
            'sharpe_ratio': calculate_sharpe_ratio(portfolio_returns, self.risk_free_rate),
            'max_drawdown': calculate_max_drawdown(portfolio_values),
            'calmar_ratio': calculate_calmar_ratio(portfolio_returns, portfolio_values),
            'win_rate': calculate_win_rate(portfolio_returns),
            'profit_loss_ratio': calculate_profit_loss_ratio(portfolio_returns),
            'var_95': calculate_var(portfolio_returns, 0.05),
            'cvar_95': calculate_cvar(portfolio_returns, 0.05)
        }
        
        # 如果有基准数据
        if benchmark_values is not None and len(benchmark_values) == len(portfolio_values):
            benchmark_returns = np.diff(benchmark_values) / benchmark_values[:-1]
            
            metrics.update({
                'beta': calculate_beta(portfolio_returns, benchmark_returns),
                'alpha': calculate_alpha(portfolio_returns, benchmark_returns, self.risk_free_rate),
                'information_ratio': calculate_information_ratio(portfolio_returns, benchmark_returns),
                'tracking_error': np.std(portfolio_returns - benchmark_returns) * np.sqrt(252)
            })
        
        return metrics
    
    def calculate_rolling_metrics(self, 
                                portfolio_values: np.ndarray,
                                window: int = 252) -> Dict[str, np.ndarray]:
        """
        计算滚动指标
        
        Args:
            portfolio_values: 投资组合价值序列
            window: 滚动窗口大小
            
        Returns:
            滚动指标字典
        """
        if len(portfolio_values) < window + 1:
            return {}
        
        rolling_metrics = {
            'rolling_return': [],
            'rolling_volatility': [],
            'rolling_sharpe': [],
            'rolling_max_drawdown': []
        }
        
        for i in range(window, len(portfolio_values)):
            window_values = portfolio_values[i-window:i+1]
            window_returns = np.diff(window_values) / window_values[:-1]
            
            rolling_metrics['rolling_return'].append(
                (window_values[-1] - window_values[0]) / window_values[0]
            )
            rolling_metrics['rolling_volatility'].append(
                np.std(window_returns) * np.sqrt(252)
            )
            rolling_metrics['rolling_sharpe'].append(
                calculate_sharpe_ratio(window_returns, self.risk_free_rate)
            )
            rolling_metrics['rolling_max_drawdown'].append(
                calculate_max_drawdown(window_values)
            )
        
        # 转换为numpy数组
        for key in rolling_metrics:
            rolling_metrics[key] = np.array(rolling_metrics[key])
        
        return rolling_metrics

def plot_metrics_comparison(metrics_dict: Dict[str, Dict[str, float]], 
                          save_path: Optional[str] = None):
    """
    绘制指标对比图
    
    Args:
        metrics_dict: 指标字典，格式为 {model_name: {metric_name: value}}
        save_path: 保存路径
    """
    if not metrics_dict:
        return
    
    # 准备数据
    models = list(metrics_dict.keys())
    metrics = list(metrics_dict[models[0]].keys())
    
    # 创建子图
    n_metrics = len(metrics)
    n_cols = 3
    n_rows = (n_metrics + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes] if n_cols == 1 else axes
    
    for i, metric in enumerate(metrics):
        if i >= len(axes):
            break
            
        values = [metrics_dict[model].get(metric, 0) for model in models]
        
        axes[i].bar(models, values, color=COLORS['profit'])
        axes[i].set_title(metric.replace('_', ' ').title())
        axes[i].set_ylabel('Value')
        axes[i].tick_params(axis='x', rotation=45)
        axes[i].grid(True, alpha=0.3)
    
    # 隐藏多余的子图
    for i in range(len(metrics), len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=DPI, bbox_inches='tight')
        plt.close()
    else:
        plt.show()

if __name__ == "__main__":
    # 测试代码
    print("测试指标计算...")
    
    # 创建模拟数据
    np.random.seed(42)
    n_days = 252
    returns = np.random.normal(0.001, 0.02, n_days)
    values = np.cumprod(1 + returns) * 100000
    
    # 计算指标
    calculator = MetricsCalculator()
    metrics = calculator.calculate_all_metrics(values)
    
    print("计算的指标:")
    for key, value in metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # 测试IC计算
    predictions = np.random.randn(100)
    targets = predictions + np.random.randn(100) * 0.5
    ic = calculate_ic(predictions, targets)
    ric = calculate_ric(predictions, targets)
    
    print(f"\nIC: {ic:.4f}")
    print(f"RIC: {ric:.4f}")
    
    print("✓ 指标计算测试通过")
